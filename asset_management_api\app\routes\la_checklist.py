# app/routes/la_checklist.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.config.database import get_db
from app.models.la_checklist import LAChecklistSession, LAChecklistItem, LA_CHECKLIST_TEMPLATE, LAChecklistSessionStatus, LAChecklistItemStatus
from app.models.assets import Asset, AssetStatus
from app.models.asset_status_history import AssetStatusHistory
from app.models.user import User
from app.middleware.auth import get_current_user
from app.schemas.la_checklist import (
    LAChecklistSessionCreate, LAChecklistSessionUpdate, LAChecklistSessionResponse,
    LAChecklistItemUpdate, LAChecklistItemResponse, LAChecklistSessionWithItems,
    LAChecklistSessionStart
)
from app.services.asset_workflow_service import AssetWorkflowService, WorkflowModules
from typing import List, Optional
import logging
import uuid
from datetime import datetime

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/available-assets", response_model=List[dict])
async def get_available_assets_for_la_checklist(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get assets that are eligible for L&A checklist (New or Ready status)."""
    try:
        assets = db.query(Asset).filter(
            Asset.status.in_([AssetStatus.NEW, AssetStatus.READY])
        ).all()
        
        return [
            {
                "id": asset.id,
                "asset_id": asset.asset_id,
                "type": asset.type,
                "model": asset.model,
                "name": f"{asset.type} - {asset.asset_id}",
                "status": asset.status,
                "location": asset.location
            }
            for asset in assets
        ]
    except Exception as e:
        logger.error(f"Error fetching available assets: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch available assets"
        )

@router.post("/sessions", response_model=LAChecklistSessionWithItems)
async def start_la_checklist_session(
    session_data: LAChecklistSessionStart,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start a new L&A checklist session for an asset."""
    try:
        # Check if asset exists and is eligible
        asset = db.query(Asset).filter(Asset.id == session_data.asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        if asset.status not in [AssetStatus.NEW, AssetStatus.READY]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Asset must be in 'New' or 'Ready' status. Current status: {asset.status}"
            )
        
        # Check if there's already an active session for this asset
        existing_session = db.query(LAChecklistSession).filter(
            and_(
                LAChecklistSession.asset_id == session_data.asset_id,
                LAChecklistSession.status == LAChecklistSessionStatus.ACTIVE
            )
        ).first()
        
        if existing_session:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Asset already has an active L&A checklist session"
            )
        
        # Generate unique session ID
        session_id = f"LA-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8]}"
        
        # Create new session
        new_session = LAChecklistSession(
            session_id=session_id,
            asset_id=session_data.asset_id,
            created_by=current_user.id,
            notes=session_data.notes
        )
        
        db.add(new_session)
        db.flush()  # To get the session ID
        
        # Create checklist items (use custom items if provided, otherwise use template)
        checklist_items = session_data.custom_items if session_data.custom_items else LA_CHECKLIST_TEMPLATE
        
        for item_data in checklist_items:
            checklist_item = LAChecklistItem(
                session_id=new_session.id,
                item_name=item_data["item_name"],
                item_description=item_data.get("item_description"),
                is_required=item_data.get("is_required", True)
            )
            db.add(checklist_item)
        
        db.commit()
        db.refresh(new_session)
        
        # Return session with items
        return db.query(LAChecklistSession).filter(LAChecklistSession.id == new_session.id).first()
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error starting L&A checklist session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start L&A checklist session"
        )

@router.get("/sessions", response_model=List[LAChecklistSessionResponse])
async def get_la_checklist_sessions(
    asset_id: Optional[int] = None,
    status: Optional[LAChecklistSessionStatus] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get L&A checklist sessions with optional filtering."""
    try:
        query = db.query(LAChecklistSession)
        
        if asset_id:
            query = query.filter(LAChecklistSession.asset_id == asset_id)
        
        if status:
            query = query.filter(LAChecklistSession.status == status)
        
        sessions = query.order_by(LAChecklistSession.created_at.desc()).all()
        return sessions
        
    except Exception as e:
        logger.error(f"Error fetching L&A checklist sessions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch L&A checklist sessions"
        )

@router.get("/sessions/{session_id}", response_model=LAChecklistSessionWithItems)
async def get_la_checklist_session(
    session_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific L&A checklist session with items."""
    try:
        session = db.query(LAChecklistSession).filter(
            LAChecklistSession.session_id == session_id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="L&A checklist session not found"
            )
        
        return session
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching L&A checklist session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch L&A checklist session"
        )

@router.put("/items/{item_id}", response_model=LAChecklistItemResponse)
async def update_checklist_item(
    item_id: int,
    item_update: LAChecklistItemUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a checklist item status."""
    try:
        item = db.query(LAChecklistItem).filter(LAChecklistItem.id == item_id).first()
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Checklist item not found"
            )
        
        # Update item
        if item_update.status:
            item.status = item_update.status
            item.checked_by = current_user.id
            item.checked_at = datetime.utcnow()
        
        if item_update.comments:
            item.comments = item_update.comments
        
        db.commit()
        db.refresh(item)
        
        logger.info(f"Checklist item {item_id} updated by user {current_user.id}")
        return item
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating checklist item: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update checklist item"
        )

@router.post("/sessions/{session_id}/complete", response_model=LAChecklistSessionResponse)
async def complete_la_checklist_session(
    session_id: str,
    notes: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Complete an L&A checklist session and update asset status accordingly.
    This endpoint evaluates all checklist items and updates the asset status to READY or FAILED.
    """
    try:
        # Get session
        session = db.query(LAChecklistSession).filter(
            LAChecklistSession.session_id == session_id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="L&A checklist session not found"
            )
        
        if session.status != LAChecklistSessionStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Session is not active"
            )
        
        # Check if all required items have been completed
        incomplete_required_items = [
            item for item in session.checklist_items 
            if item.is_required and item.status == LAChecklistItemStatus.PENDING
        ]
        
        if incomplete_required_items:
            incomplete_names = [item.item_name for item in incomplete_required_items]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Required checklist items not completed: {', '.join(incomplete_names)}"
            )
        
        # Determine overall result based on user requirements:
        # - If ANY checklist item is not selected (still Pending) → Failed
        # - If ALL checklist items are selected (Pass/Fail/Skipped) → Pass
        pending_items = [
            item for item in session.checklist_items
            if item.status == LAChecklistItemStatus.PENDING
        ]

        # If any items are still pending (not selected), the overall result is FAIL
        overall_result = "FAIL" if pending_items else "PASS"
        
        # Update session
        session.status = LAChecklistSessionStatus.COMPLETED
        session.overall_result = overall_result
        session.completed_at = datetime.utcnow()
        if notes:
            session.notes = notes
        
        # Update asset status using workflow service
        workflow_service = AssetWorkflowService(db)
        new_status = "Failed" if overall_result == "FAIL" else "Ready"
        
        success, message = workflow_service.transition_asset_status(
            asset_id=session.asset_id,
            to_status=new_status,
            workflow_module=WorkflowModules.LA_CHECKLIST,
            user_id=current_user.id,
            change_reason=f"L&A Checklist {overall_result}",
            session_id=session.session_id,
            notes=f"L&A checklist completed with {overall_result} result. {notes or ''}",
            context={
                "checklist_result": overall_result,
                "completed_items": len([item for item in session.checklist_items if item.status == LAChecklistItemStatus.PASS]),
                "pending_items": len(pending_items),
                "total_items": len(session.checklist_items),
                "pending_item_names": [item.item_name for item in pending_items]
            }
        )
        
        if not success:
            logger.warning(f"Could not update asset status for L&A session {session.session_id}: {message}")
            # Continue with session completion even if status update fails
        
        # Update asset last_checked timestamp
        asset = db.query(Asset).filter(Asset.id == session.asset_id).first()
        if asset:
            asset.last_checked = datetime.utcnow()
        
        db.commit()
        db.refresh(session)
        
        logger.info(f"L&A checklist session {session_id} completed with result {overall_result} by user {current_user.id}")
        return session
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error completing L&A checklist session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to complete L&A checklist session"
        )

@router.get("/sessions/{session_id}/summary", response_model=dict)
async def get_la_checklist_session_summary(
    session_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get summary statistics for an L&A checklist session."""
    try:
        session = db.query(LAChecklistSession).filter(
            LAChecklistSession.session_id == session_id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="L&A checklist session not found"
            )
        
        # Calculate statistics
        total_items = len(session.checklist_items)
        completed_items = len([item for item in session.checklist_items if item.status != LAChecklistItemStatus.PENDING])
        passed_items = len([item for item in session.checklist_items if item.status == LAChecklistItemStatus.PASS])
        failed_items = len([item for item in session.checklist_items if item.status == LAChecklistItemStatus.FAIL])
        skipped_items = len([item for item in session.checklist_items if item.status == LAChecklistItemStatus.SKIPPED])
        required_items = len([item for item in session.checklist_items if item.is_required])
        
        completion_percentage = (completed_items / total_items * 100) if total_items > 0 else 0
        
        return {
            "session_id": session_id,
            "status": session.status,
            "overall_result": session.overall_result,
            "total_items": total_items,
            "completed_items": completed_items,
            "passed_items": passed_items,
            "failed_items": failed_items,
            "skipped_items": skipped_items,
            "required_items": required_items,
            "completion_percentage": round(completion_percentage, 2),
            "can_complete": completed_items == total_items or (total_items - skipped_items == completed_items),
            "started_at": session.started_at,
            "completed_at": session.completed_at
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching L&A checklist session summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch session summary"
        )
