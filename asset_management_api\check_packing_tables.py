#!/usr/bin/env python3
"""
Check if packing list related tables exist and create them if needed
"""

from sqlalchemy import text, create_engine
from app.config.database import engine, get_db
from app.models.master_packing_list import MasterPackingList
from app.config.database import Base
import sys

def check_and_create_tables():
    """Check if tables exist and create them if needed"""
    
    print("🔍 Checking packing list related tables...")
    
    try:
        with engine.connect() as conn:
            # Check master_packing_lists table
            result = conn.execute(text("SHOW TABLES LIKE 'master_packing_lists'"))
            tables = result.fetchall()
            
            if len(tables) > 0:
                print("✅ master_packing_lists table exists")
                
                # Check table structure
                result = conn.execute(text("DESCRIBE master_packing_lists"))
                columns = result.fetchall()
                print("📋 Table structure:")
                for col in columns:
                    print(f"   • {col[0]}: {col[1]}")
                
                # Check record count
                result = conn.execute(text("SELECT COUNT(*) FROM master_packing_lists"))
                count = result.scalar()
                print(f"📊 Total records: {count}")
                
                if count > 0:
                    # Show sample records
                    result = conn.execute(text("SELECT id, ref_no, description, status FROM master_packing_lists LIMIT 3"))
                    records = result.fetchall()
                    print("📄 Sample records:")
                    for record in records:
                        print(f"   • ID: {record[0]}, Ref: {record[1]}, Desc: {record[2][:30]}..., Status: {record[3]}")
            else:
                print("❌ master_packing_lists table does not exist")
                print("🔧 Creating master_packing_lists table...")
                
                # Create the table
                Base.metadata.create_all(bind=engine, tables=[MasterPackingList.__table__])
                print("✅ master_packing_lists table created successfully")
            
            # Check other related tables
            related_tables = [
                'masters_consumables',
                'masters_locations', 
                'packing_locations',
                'packing_lists',
                'consumables_categories'
            ]
            
            print("\n🔍 Checking related tables:")
            for table_name in related_tables:
                result = conn.execute(text(f"SHOW TABLES LIKE '{table_name}'"))
                tables = result.fetchall()
                
                if len(tables) > 0:
                    # Get record count
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                    count = result.scalar()
                    print(f"✅ {table_name}: {count} records")
                else:
                    print(f"❌ {table_name}: table does not exist")
            
            print("\n🎯 Testing basic functionality:")
            
            # Test next ref number generation
            try:
                next_ref = MasterPackingList.generate_next_ref_no(next(get_db()))
                print(f"✅ Next ref number generation works: {next_ref}")
            except Exception as e:
                print(f"❌ Next ref number generation failed: {e}")
            
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False
    
    return True

def create_sample_data():
    """Create some sample data for testing"""
    print("\n🔧 Creating sample data...")
    
    try:
        db = next(get_db())
        
        # Check if we already have data
        existing_count = db.query(MasterPackingList).count()
        if existing_count > 0:
            print(f"📊 Already have {existing_count} master packing lists")
            return
        
        # Create sample master packing lists
        sample_lists = [
            {
                "description": "Consumable Package 1",
                "status": True,
                "items": {
                    "items": [
                        {"item": "AV Cables", "itemNo": "001", "location": "Compartment 1", "qty": 1000},
                        {"item": "ADP Supply Kit", "itemNo": "002", "location": "Compartment 2", "qty": 4000},
                        {"item": "Envelopes", "itemNo": "003", "location": "Compartment 3", "qty": 200},
                        {"item": "Printer Paper", "itemNo": "004", "location": "Main Storage", "qty": 200}
                    ]
                },
                "last_updated_by": "System Admin"
            },
            {
                "description": "Supply Package 2", 
                "status": True,
                "items": {
                    "items": [
                        {"item": "Ballot Boxes", "itemNo": "001", "location": "Warehouse A", "qty": 50},
                        {"item": "Voting Machines", "itemNo": "002", "location": "Warehouse B", "qty": 25}
                    ]
                },
                "last_updated_by": "System Admin"
            }
        ]
        
        for i, sample_data in enumerate(sample_lists, 1):
            ref_no = f"PL{i:04d}"
            
            packing_list = MasterPackingList(
                ref_no=ref_no,
                description=sample_data["description"],
                status=sample_data["status"],
                items=sample_data["items"],
                last_updated_by=sample_data["last_updated_by"],
                last_updated_date="2025-06-08"
            )
            
            db.add(packing_list)
        
        db.commit()
        print("✅ Sample data created successfully")
        
        # Verify creation
        count = db.query(MasterPackingList).count()
        print(f"📊 Total master packing lists: {count}")
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        if 'db' in locals():
            db.rollback()
    finally:
        if 'db' in locals():
            db.close()

if __name__ == "__main__":
    print("🚀 Packing List Database Check")
    print("="*50)
    
    success = check_and_create_tables()
    
    if success:
        create_sample_data()
        print("\n✅ Database check completed successfully")
        print("💡 You can now test the APIs")
    else:
        print("\n❌ Database check failed")
        sys.exit(1)
