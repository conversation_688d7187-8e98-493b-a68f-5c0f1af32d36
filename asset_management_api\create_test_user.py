#!/usr/bin/env python3
"""
Create a test user for API testing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config.database import get_db
from app.models.user import User
from sqlalchemy.orm import Session
import uuid
import bcrypt
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_user():
    """Create a test user for API testing."""
    
    db = next(get_db())
    
    try:
        # Check if test user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("✅ Test user already exists: <EMAIL>")
            return True
        
        # Create test user
        from datetime import datetime

        test_user = User(
            id=str(uuid.uuid4()),
            username="admin",
            email="<EMAIL>",
            login_id="admin",
            first_name="Test",
            last_name="Admin",
            mobile="1234567890",
            user_group="Admin Group",
            access_level="state",
            role="admin",
            status=True,
            login_enabled=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        # Set password using the model's method
        test_user.set_password("admin123")
        
        db.add(test_user)
        db.commit()
        
        print("✅ Test user created successfully!")
        print("   Email: <EMAIL>")
        print("   Password: admin123")
        print("   Role: admin")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating test user: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 Creating test user for API testing...")
    success = create_test_user()
    if success:
        print("✅ Test user ready for API testing!")
    else:
        print("❌ Failed to create test user.")
        sys.exit(1)
