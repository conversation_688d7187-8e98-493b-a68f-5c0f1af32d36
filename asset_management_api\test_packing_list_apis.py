#!/usr/bin/env python3
"""
Test script for Master Packing List APIs
This script tests all the endpoints needed for the frontend packing list functionality
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def test_endpoint(method, endpoint, data=None, headers=None):
    """Test an API endpoint and return the response"""
    url = f"{API_BASE}{endpoint}"
    
    if headers is None:
        headers = {"Content-Type": "application/json"}
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=headers)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            print(f"❌ Unsupported method: {method}")
            return None
            
        print(f"\n{'='*60}")
        print(f"🔍 Testing: {method.upper()} {endpoint}")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success")
            try:
                response_data = response.json()
                print(f"📄 Response: {json.dumps(response_data, indent=2)[:500]}...")
                return response_data
            except:
                print(f"📄 Response: {response.text[:200]}...")
                return response.text
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection Error: Could not connect to {url}")
        print("💡 Make sure the server is running on http://localhost:8000")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def main():
    print("🚀 Testing Master Packing List APIs")
    print(f"🌐 Base URL: {BASE_URL}")
    
    # Test 1: Get all master packing lists
    print("\n" + "="*60)
    print("📋 TEST 1: Get All Master Packing Lists")
    response = test_endpoint("GET", "/masters/packing-lists/")
    
    # Test 2: Get dropdown data for items
    print("\n" + "="*60)
    print("📦 TEST 2: Get Items Dropdown")
    items_response = test_endpoint("GET", "/masters/packing-lists/config/items")
    
    # Test 3: Get dropdown data for locations
    print("\n" + "="*60)
    print("📍 TEST 3: Get Locations Dropdown")
    locations_response = test_endpoint("GET", "/masters/packing-lists/config/locations")
    
    # Test 4: Get next reference number
    print("\n" + "="*60)
    print("🔢 TEST 4: Get Next Reference Number")
    ref_no_response = test_endpoint("GET", "/masters/packing-lists/config/next-ref-no")
    
    # Test 5: Create a new master packing list (this will require authentication)
    print("\n" + "="*60)
    print("➕ TEST 5: Create Master Packing List (without auth - should fail)")
    
    sample_packing_list = {
        "description": "Test Consumable Package 1",
        "status": True,
        "image": None,
        "items": [
            {
                "item": "AV Cables",
                "location": "Compartment 1",
                "qty": 1000
            },
            {
                "item": "ADP Supply Kit",
                "location": "Compartment 2", 
                "qty": 4000
            }
        ]
    }
    
    create_response = test_endpoint("POST", "/masters/packing-lists/", sample_packing_list)
    
    # Test 6: Test the regular packing lists endpoint
    print("\n" + "="*60)
    print("📋 TEST 6: Get Regular Packing Lists")
    regular_response = test_endpoint("GET", "/packing-lists/")
    
    # Test 7: Test packing lists config endpoints
    print("\n" + "="*60)
    print("⚙️ TEST 7: Get Packing Lists Config Items")
    config_items_response = test_endpoint("GET", "/packing-lists/config/items")
    
    print("\n" + "="*60)
    print("⚙️ TEST 8: Get Packing Lists Config Locations")
    config_locations_response = test_endpoint("GET", "/packing-lists/config/locations")
    
    # Summary
    print("\n" + "="*80)
    print("📊 SUMMARY")
    print("="*80)
    
    endpoints_tested = [
        ("/masters/packing-lists/", "Master Packing Lists"),
        ("/masters/packing-lists/config/items", "Items Dropdown"),
        ("/masters/packing-lists/config/locations", "Locations Dropdown"),
        ("/masters/packing-lists/config/next-ref-no", "Next Ref Number"),
        ("/packing-lists/", "Regular Packing Lists"),
        ("/packing-lists/config/items", "Packing Lists Items Config"),
        ("/packing-lists/config/locations", "Packing Lists Locations Config")
    ]
    
    print("✅ Endpoints that should work without authentication:")
    for endpoint, name in endpoints_tested:
        print(f"   • {name}: {endpoint}")
    
    print("\n❌ Endpoints that require authentication:")
    print("   • Create Master Packing List: POST /masters/packing-lists/")
    print("   • Update Master Packing List: PUT /masters/packing-lists/{id}")
    print("   • Delete Master Packing List: DELETE /masters/packing-lists/{id}")
    
    print("\n💡 Next Steps:")
    print("   1. Test with proper authentication headers")
    print("   2. Verify frontend integration")
    print("   3. Test CRUD operations")
    print("   4. Test file upload functionality")

if __name__ == "__main__":
    main()
