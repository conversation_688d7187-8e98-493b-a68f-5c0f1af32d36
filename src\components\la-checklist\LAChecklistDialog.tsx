import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, Clock, AlertCircle, Play, Save } from "lucide-react";

interface Asset {
  id: number;
  asset_id: string;
  type: string;
  model: string;
  name: string;
  status: string;
  location: string;
}

interface ChecklistItem {
  id: number;
  item_name: string;
  item_description: string;
  is_required: boolean;
  status: 'Pending' | 'Pass' | 'Fail' | 'Skipped';
  checked_by?: number;
  comments?: string;
  checked_at?: string;
}

interface LAChecklistSession {
  id: number;
  session_id: string;
  asset_id: number;
  status: 'Active' | 'Completed' | 'Cancelled';
  overall_result?: 'PASS' | 'FAIL';
  notes?: string;
  started_at: string;
  completed_at?: string;
  checklist_items: ChecklistItem[];
}

interface LAChecklistDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSessionComplete?: (session: LAChecklistSession) => void;
}

export function LAChecklistDialog({ open, onOpenChange, onSessionComplete }: LAChecklistDialogProps) {
  const [step, setStep] = useState<'select' | 'checklist'>('select');
  const [availableAssets, setAvailableAssets] = useState<Asset[]>([]);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [currentSession, setCurrentSession] = useState<LAChecklistSession | null>(null);
  const [notes, setNotes] = useState("");
  const [loading, setLoading] = useState(false);
  const [itemComments, setItemComments] = useState<Record<number, string>>({});

  // Load available assets when dialog opens
  useEffect(() => {
    if (open && step === 'select') {
      loadAvailableAssets();
    }
  }, [open, step]);

  const loadAvailableAssets = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/la-checklist/available-assets', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const assets = await response.json();
        setAvailableAssets(assets);
      } else {
        console.error('Failed to load available assets');
      }
    } catch (error) {
      console.error('Error loading assets:', error);
    } finally {
      setLoading(false);
    }
  };

  const startSession = async () => {
    if (!selectedAsset) return;

    try {
      setLoading(true);
      const response = await fetch('/api/la-checklist/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          asset_id: selectedAsset.id,
          notes: notes
        })
      });

      if (response.ok) {
        const session = await response.json();
        setCurrentSession(session);
        setStep('checklist');
      } else {
        console.error('Failed to start session');
      }
    } catch (error) {
      console.error('Error starting session:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateChecklistItem = async (itemId: number, status: ChecklistItem['status'], comments?: string) => {
    if (!currentSession) return;

    try {
      const response = await fetch(`/api/la-checklist/items/${itemId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          status: status,
          comments: comments
        })
      });

      if (response.ok) {
        const updatedItem = await response.json();
        
        // Update the current session
        setCurrentSession(prev => {
          if (!prev) return prev;
          
          return {
            ...prev,
            checklist_items: prev.checklist_items.map(item =>
              item.id === itemId ? updatedItem : item
            )
          };
        });
      } else {
        console.error('Failed to update checklist item');
      }
    } catch (error) {
      console.error('Error updating checklist item:', error);
    }
  };

  const completeSession = async () => {
    if (!currentSession) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/la-checklist/sessions/${currentSession.session_id}/complete`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        // Fetch updated session
        const sessionResponse = await fetch(`/api/la-checklist/sessions/${currentSession.session_id}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (sessionResponse.ok) {
          const completedSession = await sessionResponse.json();
          setCurrentSession(completedSession);
          
          if (onSessionComplete) {
            onSessionComplete(completedSession);
          }
        }
      } else {
        console.error('Failed to complete session');
      }
    } catch (error) {
      console.error('Error completing session:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setStep('select');
    setSelectedAsset(null);
    setCurrentSession(null);
    setNotes("");
    setItemComments({});
    onOpenChange(false);
  };

  const getStatusIcon = (status: ChecklistItem['status']) => {
    switch (status) {
      case 'Pass':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'Fail':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'Skipped':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: ChecklistItem['status']) => {
    const colors = {
      'Pending': 'bg-gray-100 text-gray-700',
      'Pass': 'bg-green-100 text-green-700',
      'Fail': 'bg-red-100 text-red-700',
      'Skipped': 'bg-yellow-100 text-yellow-700'
    };

    return (
      <Badge className={colors[status]}>
        {getStatusIcon(status)}
        <span className="ml-1">{status}</span>
      </Badge>
    );
  };

  // Updated logic: All items (required and optional) must be selected to complete
  // Backend will determine Pass/Fail based on whether any items are still pending
  const completedItems = currentSession?.checklist_items.filter(
    item => item.status !== 'Pending'
  ).length || 0;

  const totalItems = currentSession?.checklist_items.length || 0;

  const canComplete = completedItems === totalItems;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            L&A Checklist Session
          </DialogTitle>
        </DialogHeader>

        {step === 'select' && (
          <div className="space-y-6">
            <div>
              <Label htmlFor="asset-select">Select Asset for L&A Testing</Label>
              <Select
                value={selectedAsset?.id.toString() || ""}
                onValueChange={(value) => {
                  const asset = availableAssets.find(a => a.id.toString() === value);
                  setSelectedAsset(asset || null);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose an asset to test..." />
                </SelectTrigger>
                <SelectContent>
                  {availableAssets.map((asset) => (
                    <SelectItem key={asset.id} value={asset.id.toString()}>
                      <div className="flex flex-col">
                        <span className="font-medium">{asset.name}</span>
                        <span className="text-sm text-gray-500">
                          {asset.asset_id} • {asset.status} • {asset.location}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="notes">Session Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Add any notes about this testing session..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex justify-end gap-3">
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button 
                onClick={startSession} 
                disabled={!selectedAsset || loading}
              >
                Start L&A Session
              </Button>
            </div>
          </div>
        )}

        {step === 'checklist' && currentSession && (
          <div className="space-y-6">
            {/* Session Header */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Session: {currentSession.session_id}</span>
                  <Badge className={
                    currentSession.status === 'Completed' 
                      ? currentSession.overall_result === 'PASS' 
                        ? 'bg-green-100 text-green-700'
                        : 'bg-red-100 text-red-700'
                      : 'bg-blue-100 text-blue-700'
                  }>
                    {currentSession.status === 'Completed' 
                      ? currentSession.overall_result 
                      : currentSession.status}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Asset:</span> {selectedAsset?.name}
                  </div>
                  <div>
                    <span className="font-medium">Asset ID:</span> {selectedAsset?.asset_id}
                  </div>
                  <div>
                    <span className="font-medium">Progress:</span> {completedItems}/{totalItems} items completed
                  </div>
                  <div>
                    <span className="font-medium">Started:</span> {new Date(currentSession.started_at).toLocaleString()}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Checklist Items */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Checklist Items</h3>
              
              {currentSession.checklist_items.map((item) => (
                <Card key={item.id}>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{item.item_name}</h4>
                            {item.is_required && (
                              <Badge variant="outline" className="text-xs">Required</Badge>
                            )}
                          </div>
                          {item.item_description && (
                            <p className="text-sm text-gray-600 mt-1">{item.item_description}</p>
                          )}
                        </div>
                        <div className="ml-4">
                          {getStatusBadge(item.status)}
                        </div>
                      </div>

                      {currentSession.status === 'Active' && item.status === 'Pending' && (
                        <div className="space-y-3">
                          <div>
                            <Label htmlFor={`comments-${item.id}`}>Comments (Optional)</Label>
                            <Input
                              id={`comments-${item.id}`}
                              placeholder="Add comments about this check..."
                              value={itemComments[item.id] || ""}
                              onChange={(e) => setItemComments(prev => ({
                                ...prev,
                                [item.id]: e.target.value
                              }))}
                            />
                          </div>
                          
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                              onClick={() => updateChecklistItem(item.id, 'Pass', itemComments[item.id])}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Pass
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => updateChecklistItem(item.id, 'Fail', itemComments[item.id])}
                            >
                              <XCircle className="h-4 w-4 mr-1" />
                              Fail
                            </Button>
                            {!item.is_required && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => updateChecklistItem(item.id, 'Skipped', itemComments[item.id])}
                              >
                                <AlertCircle className="h-4 w-4 mr-1" />
                                Skip
                              </Button>
                            )}
                          </div>
                        </div>
                      )}

                      {item.status !== 'Pending' && item.comments && (
                        <div className="bg-gray-50 p-3 rounded">
                          <p className="text-sm"><strong>Comments:</strong> {item.comments}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between">
              <Button variant="outline" onClick={handleClose}>
                Close
              </Button>
              
              {currentSession.status === 'Active' && (
                <Button 
                  onClick={completeSession}
                  disabled={!canComplete || loading}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Complete Session
                </Button>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
