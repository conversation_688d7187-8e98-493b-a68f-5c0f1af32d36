import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { PageTitle } from "@/components/layout/PageTitle";
import { Plus } from "lucide-react";
import { exportData } from "@/utils/exportUtils";
import { laChecklistService, LAChecklistSession, LAChecklistAsset } from "@/services/laChecklistService";
import { LAChecklistDialog } from "@/components/la-checklist/LAChecklistDialog";

const SimpleLAChecklistPage = () => {
  // Backend data states
  const [testSessionData, setTestSessionData] = useState<LAChecklistSession[]>([]);
  const [availableAssets, setAvailableAssets] = useState<LAChecklistAsset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showLADialog, setShowLADialog] = useState(false);

  // Load data from backend
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load available assets and sessions from backend
        const [assets, sessions] = await Promise.all([
          laChecklistService.getAvailableAssets(),
          laChecklistService.getSessions()
        ]);

        setAvailableAssets(assets);
        setTestSessionData(sessions);
      } catch (err) {
        console.error('Error loading L&A data:', err);
        setError('Failed to load L&A checklist data. Please ensure the backend is running.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Handle export based on format
  const handleExport = (format: string) => {
    exportData(
      testSessionData,
      format,
      "LA_Checklist_Sessions",
      "Logic & Accuracy Test Sessions",
      ["session_id", "asset_id", "status", "started_at", "overall_result"]
    );
  };

  // Calculate statistics from backend data
  const stats = {
    totalAssets: availableAssets.length,
    completedSessions: testSessionData.filter(s => s.status === 'Completed').length,
    activeSessions: testSessionData.filter(s => s.status === 'Active').length,
    passedSessions: testSessionData.filter(s => s.overall_result === 'PASS').length,
    failedSessions: testSessionData.filter(s => s.overall_result === 'FAIL').length,
  };

  const handleSessionComplete = (session: LAChecklistSession) => {
    // Refresh data after session completion
    setTestSessionData(prev => prev.map(s => s.id === session.id ? session : s));
    setShowLADialog(false);
  };

  return (
    <AppLayout>
      <div className="p-4 md:p-6 lg:p-8">
        <PageTitle
          title="Logic & Accuracy Checklist"
          description="Manage and track Logic & Accuracy testing for election equipment"
          onExport={(format) => handleExport(format)}
          actions={
            <Button size="sm" onClick={() => setShowLADialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Test Session
            </Button>
          }
        />

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="text-lg">Loading L&A checklist data...</div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="text-red-800 font-medium">Error Loading Data</div>
            <div className="text-red-600 text-sm mt-1">{error}</div>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </div>
        ) : (
          <>
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <div className="border p-6 rounded-lg shadow-sm">
                <h3 className="text-lg font-semibold mb-2">Available Assets</h3>
                <p className="text-3xl font-bold text-blue-600">{stats.totalAssets}</p>
                <p className="text-sm text-gray-600">Ready for L&A testing</p>
              </div>

              <div className="border p-6 rounded-lg shadow-sm">
                <h3 className="text-lg font-semibold mb-2">Completed Sessions</h3>
                <p className="text-3xl font-bold text-green-600">{stats.completedSessions}</p>
                <p className="text-sm text-gray-600">Testing completed</p>
              </div>

              <div className="border p-6 rounded-lg shadow-sm">
                <h3 className="text-lg font-semibold mb-2">Active Sessions</h3>
                <p className="text-3xl font-bold text-yellow-600">{stats.activeSessions}</p>
                <p className="text-sm text-gray-600">Currently in progress</p>
              </div>

              <div className="border p-6 rounded-lg shadow-sm">
                <h3 className="text-lg font-semibold mb-2">Pass Rate</h3>
                <p className="text-3xl font-bold text-purple-600">
                  {stats.completedSessions > 0
                    ? Math.round((stats.passedSessions / stats.completedSessions) * 100)
                    : 0}%
                </p>
                <p className="text-sm text-gray-600">{stats.passedSessions} passed, {stats.failedSessions} failed</p>
              </div>
            </div>
          </>
        )}

        <h2 className="text-xl font-semibold mb-4">Recent L&A Test Sessions</h2>
        <div className="border rounded-lg shadow-sm overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-4 font-medium">Session ID</th>
                <th className="text-left py-3 px-4 font-medium">Equipment Type</th>
                <th className="text-left py-3 px-4 font-medium">County</th>
                <th className="text-left py-3 px-4 font-medium">Date</th>
                <th className="text-left py-3 px-4 font-medium">Status</th>
              </tr>
            </thead>
            <tbody>
              {testSessionData.length === 0 ? (
                <tr>
                  <td colSpan={5} className="py-8 px-4 text-center text-gray-500">
                    No L&A test sessions found. Click "New Test Session" to start testing.
                  </td>
                </tr>
              ) : (
                testSessionData.map((session) => (
                  <tr key={session.id} className="border-t">
                    <td className="py-3 px-4">{session.session_id}</td>
                    <td className="py-3 px-4">Asset #{session.asset_id}</td>
                    <td className="py-3 px-4">-</td>
                    <td className="py-3 px-4">{new Date(session.started_at).toLocaleDateString()}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        session.status === 'Completed'
                          ? session.overall_result === 'PASS'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {session.status === 'Completed'
                          ? `${session.status} (${session.overall_result})`
                          : session.status}
                      </span>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* L&A Checklist Dialog */}
        <LAChecklistDialog
          open={showLADialog}
          onOpenChange={setShowLADialog}
          onSessionComplete={handleSessionComplete}
        />
      </div>
    </AppLayout>
  );
};

export default SimpleLAChecklistPage;
