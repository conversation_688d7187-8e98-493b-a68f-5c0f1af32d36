import React, { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { PageTitle } from "@/components/layout/PageTitle";
import { Plus } from "lucide-react";
import { exportData } from "@/utils/exportUtils";

const SimpleLAChecklistPage = () => {
  // Data will be loaded from backend API
  const [testSessionData, setTestSessionData] = useState([]);

  // Handle export based on format
  const handleExport = (format: string) => {
    exportData(
      testSessionData,
      format,
      "LA_Checklist_Sessions",
      "Logic & Accuracy Test Sessions",
      ["id", "equipment", "county", "date", "status"]
    );
  };

  return (
    <AppLayout>
      <div className="p-4 md:p-6 lg:p-8">
        <PageTitle
          title="Logic & Accuracy Checklist"
          description="Manage and track Logic & Accuracy testing for election equipment"
          onExport={(format) => handleExport(format)}
          actions={
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              New Test Session
            </Button>
          }
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="border p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Pollpads</h2>
            <p className="text-gray-600 mb-4">Logic & Accuracy testing for Pollpads</p>
            <div className="flex justify-between text-sm mb-2">
              <span>Progress:</span>
              <span className="font-medium">85%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div className="bg-blue-600 h-2 rounded-full" style={{ width: "85%" }}></div>
            </div>
            <Link to="/la-checklist/pollpads">
              <Button className="w-full">View Details</Button>
            </Link>
          </div>

          <div className="border p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Scanners</h2>
            <p className="text-gray-600 mb-4">Logic & Accuracy testing for Scanners</p>
            <div className="flex justify-between text-sm mb-2">
              <span>Progress:</span>
              <span className="font-medium">60%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div className="bg-purple-600 h-2 rounded-full" style={{ width: "60%" }}></div>
            </div>
            <Link to="/la-checklist/scanners">
              <Button className="w-full">View Details</Button>
            </Link>
          </div>

          <div className="border p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-semibold mb-4">BMDs</h2>
            <p className="text-gray-600 mb-4">Logic & Accuracy testing for Ballot Marking Devices</p>
            <div className="flex justify-between text-sm mb-2">
              <span>Progress:</span>
              <span className="font-medium">45%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div className="bg-green-600 h-2 rounded-full" style={{ width: "45%" }}></div>
            </div>
            <Link to="/la-checklist/bmd">
              <Button className="w-full">View Details</Button>
            </Link>
          </div>
        </div>

        <h2 className="text-xl font-semibold mb-4">Recent L&A Test Sessions</h2>
        <div className="border rounded-lg shadow-sm overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-4 font-medium">Session ID</th>
                <th className="text-left py-3 px-4 font-medium">Equipment Type</th>
                <th className="text-left py-3 px-4 font-medium">County</th>
                <th className="text-left py-3 px-4 font-medium">Date</th>
                <th className="text-left py-3 px-4 font-medium">Status</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-t">
                <td className="py-3 px-4">LA-1001</td>
                <td className="py-3 px-4">Pollpads</td>
                <td className="py-3 px-4">Richland</td>
                <td className="py-3 px-4">04/15/2023</td>
                <td className="py-3 px-4">
                  <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                    Completed
                  </span>
                </td>
              </tr>
              <tr className="border-t">
                <td className="py-3 px-4">LA-1002</td>
                <td className="py-3 px-4">Scanners</td>
                <td className="py-3 px-4">Lexington</td>
                <td className="py-3 px-4">04/16/2023</td>
                <td className="py-3 px-4">
                  <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                    Completed
                  </span>
                </td>
              </tr>
              <tr className="border-t">
                <td className="py-3 px-4">LA-1003</td>
                <td className="py-3 px-4">BMDs</td>
                <td className="py-3 px-4">Charleston</td>
                <td className="py-3 px-4">04/17/2023</td>
                <td className="py-3 px-4">
                  <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
                    In Progress
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </AppLayout>
  );
};

export default SimpleLAChecklistPage;
