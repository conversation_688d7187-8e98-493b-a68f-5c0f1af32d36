import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Eye, Pencil, Plus, Search, MoreHorizontal, FileDown, ArrowLeft } from "lucide-react";
import { PageTitle } from "@/components/layout/PageTitle";
import { BMDViewDialog } from "@/components/la-checklist/BMDViewDialog";
import { BMDEditDialog } from "@/components/la-checklist/BMDEditDialog";
import { laChecklistService, LAChecklistAsset, LAChecklistSession } from "@/services/laChecklistService";
import { LAChecklistDialog } from "@/components/la-checklist/LAChecklistDialog";

const SimpleBMDPage = () => {
  // Backend data states
  const [bmdAssets, setBmdAssets] = useState<LAChecklistAsset[]>([]);
  const [bmdSessions, setBmdSessions] = useState<LAChecklistSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showLADialog, setShowLADialog] = useState(false);

  // Load BMD data from backend
  useEffect(() => {
    const loadBMDData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [assets, sessions] = await Promise.all([
          laChecklistService.getBMDAssets(),
          laChecklistService.getSessions()
        ]);

        setBmdAssets(assets);
        // Filter sessions for BMD assets only
        const bmdAssetIds = assets.map(a => a.id);
        setBmdSessions(sessions.filter(s => bmdAssetIds.includes(s.asset_id)));
      } catch (err) {
        console.error('Error loading BMD data:', err);
        setError('Failed to load BMD data. Please ensure the backend is running.');
      } finally {
        setLoading(false);
      }
    };

    loadBMDData();
  }, []);

  // Convert backend data to display format
  const bmdData = bmdAssets.map(asset => {
    const session = bmdSessions.find(s => s.asset_id === asset.id);
    return {
      id: asset.id,
      election: "Current Election",
      location: asset.location || "Unknown",
      tabulator: asset.asset_id,
      date: session ? new Date(session.started_at).toLocaleDateString() : new Date().toLocaleDateString(),
      status: session ? (session.status === 'Completed' ? session.overall_result || 'Completed' : session.status) : 'Pending',
      // Default checklist values - these would come from session details in a real implementation
      confirmVersion: true,
      confirmTechnicianCard: true,
      clearElectionData: true,
      loadElectionData: true,
      confirmPollWorkerCard: true,
      selectProperTabulator: true,
      manualSelectionActive: true,
      openPolls: true,
      verifyPollPadVoterCardPP: true,
      verifyPollPadVoterCardPWC: true,
      createBMDgeneratedBallot: true,
      closePolls: true,
      resetCountToZero: true,
      publicCounter: "0",
      powerOff: true,
      topLeftSeal: "123",
      topRightSeal: "124",
      bottomRightSeal: "134",
      initialsOfInspector: "TS",
      technician: "System User"
    };
  });

  // State for dialogs
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Filter data based on search term
  const filteredData = bmdData.filter(item =>
    item.election.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.tabulator.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.technician.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle view item
  const handleViewItem = (item: any) => {
    console.log("View item clicked:", item);
    setSelectedItem({...item}); // Create a new object to ensure state updates
    setShowViewDialog(true);
  };

  // Handle edit item
  const handleEditItem = (item: any) => {
    console.log("Edit item clicked:", item);
    setSelectedItem({...item}); // Create a new object to ensure state updates
    setShowEditDialog(true);
  };

  // Handle add new item
  const handleAddItem = () => {
    setSelectedItem(null);
    setShowAddDialog(true);
  };

  // Handle save item (for both add and edit)
  const handleSaveItem = (data: any) => {
    if (data.id) {
      // Edit existing item
      setBmdData(bmdData.map(item =>
        item.id === data.id ? { ...data } : item
      ));
    } else {
      // Add new item
      const newItem = {
        ...data,
        id: bmdData.length > 0 ? Math.max(...bmdData.map(item => item.id)) + 1 : 1
      };
      setBmdData([...bmdData, newItem]);
    }
  };

  // Handle edit from view dialog
  const handleEditFromView = () => {
    setShowViewDialog(false);
    setShowEditDialog(true);
  };

  // Format data for export
  const exportData = bmdData.map(item => ({
    id: `BMD-${item.id.toString().padStart(4, '0')}`,
    election: item.election,
    location: item.location,
    tabulator: item.tabulator,
    testDate: item.date,
    technician: item.technician
  }));

  return (
    <AppLayout>
      <div className="p-4 md:p-6 lg:p-8">
        <div className="mb-4">
          <Link to="/la-checklist">
            <Button variant="ghost" size="sm" className="mr-2">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to L&A Checklist
            </Button>
          </Link>
        </div>

        <PageTitle
          title="BMD L&A Testing"
          description="Manage and track Logic & Accuracy testing for Ballot Marking Devices"
          exportData={exportData}
          exportFilename="BMD_LA_Testing_Units"
          actions={
            <Button size="sm" onClick={() => setShowLADialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Test Session
            </Button>
          }
        />



        <div className="mb-4 flex justify-between items-center">
          <h2 className="text-xl font-semibold">BMD Units</h2>
          <div className="relative w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="border rounded-lg shadow-sm overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-4 font-medium">BMD ID</th>
                <th className="text-left py-3 px-4 font-medium">Election</th>
                <th className="text-left py-3 px-4 font-medium">Location</th>
                <th className="text-left py-3 px-4 font-medium">Tabulator</th>
                <th className="text-left py-3 px-4 font-medium">Test Date</th>
                <th className="text-left py-3 px-4 font-medium">Technician</th>
                <th className="text-left py-3 px-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.map((item) => (
                <tr key={item.id} className="border-t hover:bg-gray-50">
                  <td className="py-3 px-4">BMD-{item.id.toString().padStart(4, '0')}</td>
                  <td className="py-3 px-4">{item.election}</td>
                  <td className="py-3 px-4">{item.location}</td>
                  <td className="py-3 px-4">{item.tabulator}</td>
                  <td className="py-3 px-4">{item.date}</td>
                  <td className="py-3 px-4">{item.technician}</td>
                  <td className="py-3 px-4">
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm" onClick={() => handleViewItem(item)}>
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleEditItem(item)}>
                        <Pencil className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
              {filteredData.length === 0 && (
                <tr>
                  <td colSpan={7} className="p-4 text-center text-gray-500">
                    No records found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* These dialogs are for editing existing BMD records, not for L&A testing */}
        {/* Add Dialog */}
        <BMDEditDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          data={null}
          onSave={handleSaveItem}
          isEditMode={false}
        />

        {/* Edit Dialog */}
        <BMDEditDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          data={selectedItem}
          onSave={handleSaveItem}
          isEditMode={true}
        />

        {/* View Dialog */}
        <BMDViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          data={selectedItem}
          onEdit={handleEditFromView}
        />

        {/* L&A Checklist Dialog for BMD */}
        <LAChecklistDialog
          open={showLADialog}
          onOpenChange={setShowLADialog}
          onSessionComplete={(session) => {
            // Refresh data after session completion
            setBmdSessions(prev => [...prev, session]);
            setShowLADialog(false);
          }}
        />
      </div>
    </AppLayout>
  );
};

export default SimpleBMDPage;
