import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Plus, Filter } from "lucide-react";
import { PageTitle } from "@/components/layout/PageTitle";
import { exportData } from "@/utils/exportUtils";
import { laChecklistService, LAChecklistAsset, LAChecklistSession } from "@/services/laChecklistService";
import { LAChecklistDialog } from "@/components/la-checklist/LAChecklistDialog";

const SimplePollpadsPage = () => {
  // Backend data states
  const [pollpadAssets, setPollpadAssets] = useState<LAChecklistAsset[]>([]);
  const [pollpadSessions, setPollpadSessions] = useState<LAChecklistSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showLADialog, setShowLADialog] = useState(false);

  // Load pollpad data from backend
  useEffect(() => {
    const loadPollpadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [assets, sessions] = await Promise.all([
          laChecklistService.getPollpadAssets(),
          laChecklistService.getSessions()
        ]);

        setPollpadAssets(assets);
        // Filter sessions for pollpad assets only
        const pollpadAssetIds = assets.map(a => a.id);
        setPollpadSessions(sessions.filter(s => pollpadAssetIds.includes(s.asset_id)));
      } catch (err) {
        console.error('Error loading pollpad data:', err);
        setError('Failed to load pollpad data. Please ensure the backend is running.');
      } finally {
        setLoading(false);
      }
    };

    loadPollpadData();
  }, []);

  // Prepare data for export
  const pollpadUnitsData = pollpadAssets.map(asset => {
    const session = pollpadSessions.find(s => s.asset_id === asset.id);
    return {
      id: asset.asset_id,
      serialNumber: asset.serial_number || '-',
      county: asset.county || '-',
      testDate: session ? new Date(session.started_at).toLocaleDateString() : '-',
      status: session ? (session.status === 'Completed' ? session.overall_result || 'Completed' : session.status) : 'Pending'
    };
  });

  // Handle export based on format
  const handleExport = (format: string) => {
    exportData(
      pollpadUnitsData,
      format,
      "Pollpad_LA_Testing_Units",
      "Pollpad L&A Testing Units",
      ["id", "serialNumber", "county", "testDate", "status"]
    );
  };

  return (
    <AppLayout>
      <div className="p-4 md:p-6 lg:p-8">
        <div className="mb-4">
          <Link to="/la-checklist">
            <Button variant="ghost" size="sm" className="mr-2">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to L&A Checklist
            </Button>
          </Link>
        </div>

        <PageTitle
          title="Pollpad L&A Testing"
          description="Manage and track Logic & Accuracy testing for Pollpads"
          onExport={(format) => handleExport(format)}
          actions={
            <>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button size="sm" onClick={() => setShowLADialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                New Test Session
              </Button>
            </>
          }
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="border p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Testing Progress</h2>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">85%</div>
              <p className="text-sm text-gray-600">102 of 120 units tested</p>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: "85%" }}></div>
              </div>
            </div>
          </div>

          <div className="border p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <div className="grid grid-cols-2 gap-4">
              <div className="border rounded-lg p-4 text-center">
                <p className="text-2xl font-bold text-green-600">98</p>
                <p className="text-sm text-gray-600">Passed</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-2xl font-bold text-red-600">4</p>
                <p className="text-sm text-gray-600">Failed</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-2xl font-bold text-yellow-600">18</p>
                <p className="text-sm text-gray-600">Pending</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-2xl font-bold text-blue-600">6</p>
                <p className="text-sm text-gray-600">Retest</p>
              </div>
            </div>
          </div>

          <div className="border p-6 rounded-lg shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Test Categories</h2>
            <div className="space-y-4">
              <Link to="/la-checklist/pollpads/voter-card-testing">
                <div className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
                  <p className="font-medium">Voter Card Testing</p>
                  <p className="text-sm text-gray-600 mt-1">Test voter card encoding and reading</p>
                </div>
              </Link>
              <Link to="/la-checklist/pollpads/voter-file-loaded">
                <div className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
                  <p className="font-medium">Voter File Loaded</p>
                  <p className="text-sm text-gray-600 mt-1">Verify voter file loading and integrity</p>
                </div>
              </Link>

            </div>
          </div>
        </div>

        <h2 className="text-xl font-semibold mb-4">Pollpad Units</h2>
        <div className="border rounded-lg shadow-sm overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-4 font-medium">Pollpad ID</th>
                <th className="text-left py-3 px-4 font-medium">Serial Number</th>
                <th className="text-left py-3 px-4 font-medium">County</th>
                <th className="text-left py-3 px-4 font-medium">Test Date</th>
                <th className="text-left py-3 px-4 font-medium">Status</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={5} className="py-8 px-4 text-center text-gray-500">
                    Loading pollpad data...
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td colSpan={5} className="py-8 px-4 text-center text-red-500">
                    {error}
                  </td>
                </tr>
              ) : pollpadUnitsData.length === 0 ? (
                <tr>
                  <td colSpan={5} className="py-8 px-4 text-center text-gray-500">
                    No pollpad assets found. Add pollpads in Masters/Assets.
                  </td>
                </tr>
              ) : (
                pollpadUnitsData.map((unit, index) => (
                  <tr key={unit.id} className="border-t">
                    <td className="py-3 px-4">{unit.id}</td>
                    <td className="py-3 px-4">{unit.serialNumber}</td>
                    <td className="py-3 px-4">{unit.county}</td>
                    <td className="py-3 px-4">{unit.testDate}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        unit.status === 'PASS' || unit.status === 'Passed'
                          ? 'bg-green-100 text-green-800'
                          : unit.status === 'FAIL' || unit.status === 'Failed'
                          ? 'bg-red-100 text-red-800'
                          : unit.status === 'Active'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {unit.status}
                      </span>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* L&A Checklist Dialog for Pollpads */}
        <LAChecklistDialog
          open={showLADialog}
          onOpenChange={setShowLADialog}
          onSessionComplete={(session) => {
            // Refresh data after session completion
            setPollpadSessions(prev => [...prev, session]);
            setShowLADialog(false);
          }}
        />
      </div>
    </AppLayout>
  );
};

export default SimplePollpadsPage;
