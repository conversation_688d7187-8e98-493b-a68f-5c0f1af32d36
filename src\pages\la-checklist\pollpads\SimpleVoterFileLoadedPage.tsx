import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Eye, Pencil, Plus, Search, MoreHorizontal, FileDown, ArrowLeft } from "lucide-react";
import { VoterFileLoadedViewDialog } from "@/components/la-checklist/VoterFileLoadedViewDialog";
import { VoterFileLoadedEditDialog } from "@/components/la-checklist/VoterFileLoadedEditDialog";
import { laChecklistService, VoterFileLoadResult } from "@/services/laChecklistService";
import { LAChecklistDialog } from "@/components/la-checklist/LAChecklistDialog";

const SimpleVoterFileLoadedPage = () => {
  // Backend data states
  const [voterFileData, setVoterFileData] = useState<VoterFileLoadResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showLADialog, setShowLADialog] = useState(false);

  // Load voter file data from backend
  useEffect(() => {
    const loadVoterFileData = async () => {
      try {
        setLoading(true);
        setError(null);

        const results = await laChecklistService.getVoterFileLoadResults();
        setVoterFileData(results);
      } catch (err) {
        console.error('Error loading voter file data:', err);
        setError('Failed to load voter file data. Please ensure the backend is running.');
      } finally {
        setLoading(false);
      }
    };

    loadVoterFileData();
  }, []);

  // Use backend data
  const testData = voterFileData;

  // State for dialogs
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Filter data based on search term
  const filteredData = testData.filter(item =>
    item.election.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.pollPad.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.fileVersion.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.completedBy.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle view item
  const handleViewItem = (item: any) => {
    setSelectedItem(item);
    setShowViewDialog(true);
  };

  // Handle edit item
  const handleEditItem = (item: any) => {
    setSelectedItem(item);
    setShowEditDialog(true);
  };

  // Handle add new item
  const handleAddItem = () => {
    setSelectedItem(null);
    setShowAddDialog(true);
  };

  // Handle save item (for both add and edit)
  const handleSaveItem = (data: any) => {
    if (data.id) {
      // Edit existing item
      setTestData(testData.map(item =>
        item.id === data.id ? { ...data } : item
      ));
    } else {
      // Add new item
      const newItem = {
        ...data,
        id: testData.length > 0 ? Math.max(...testData.map(item => item.id)) + 1 : 1
      };
      setTestData([...testData, newItem]);
    }
  };

  // Handle edit from view dialog
  const handleEditFromView = () => {
    setShowViewDialog(false);
    setShowEditDialog(true);
  };

  return (
    <AppLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Voter File Loaded</h1>
            <div className="flex items-center text-sm text-gray-500 mt-1">
              <Link to="/la-checklist" className="hover:underline">L&A Checklist</Link>
              <span className="mx-2">›</span>
              <Link to="/la-checklist/pollpads" className="hover:underline">Pollpads</Link>
              <span className="mx-2">›</span>
              <span>Voter File Loaded</span>
            </div>
          </div>
          <Button onClick={() => setShowLADialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Test Session
          </Button>
        </div>

        <div className="bg-white rounded-lg border shadow-sm">
          <div className="p-4 border-b flex justify-between items-center">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button variant="outline" size="sm">
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-50 text-left">
                  <th className="p-3 font-medium">Election</th>
                  <th className="p-3 font-medium">Location</th>
                  <th className="p-3 font-medium">Poll Pad</th>
                  <th className="p-3 font-medium">File Version</th>
                  <th className="p-3 font-medium">Date</th>
                  <th className="p-3 font-medium">Completed By</th>
                  <th className="p-3 font-medium text-right">Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={7} className="p-8 text-center text-gray-500">
                      Loading voter file data...
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan={7} className="p-8 text-center text-red-500">
                      {error}
                    </td>
                  </tr>
                ) : filteredData.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="p-8 text-center text-gray-500">
                      No voter file records found. Start a new L&A test session to create records.
                    </td>
                  </tr>
                ) : (
                  filteredData.map((item) => (
                    <tr key={item.id} className="border-t hover:bg-gray-50">
                      <td className="p-3">{item.election}</td>
                      <td className="p-3">{item.location}</td>
                      <td className="p-3">{item.pollPad}</td>
                      <td className="p-3">{item.fileVersion}</td>
                      <td className="p-3">{item.date}</td>
                      <td className="p-3">{item.completedBy}</td>
                      <td className="p-3 text-right">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          item.status === 'Pass'
                            ? 'bg-green-100 text-green-800'
                            : item.status === 'Fail'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {item.status}
                        </span>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          <div className="p-4 border-t flex justify-between items-center">
            <div>
              Showing {filteredData.length} of {testData.length} entries
            </div>
            <div className="flex gap-1">
              <Button variant="outline" size="sm" disabled={filteredData.length === 0}>
                Previous
              </Button>
              <Button variant="outline" size="sm" className="px-3" disabled={filteredData.length === 0}>
                1
              </Button>
              <Button variant="outline" size="sm" disabled={filteredData.length === 0}>
                Next
              </Button>
            </div>
          </div>
        </div>

        {/* Add Dialog */}
        <VoterFileLoadedEditDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          data={null}
          onSave={handleSaveItem}
          isEditMode={false}
        />

        {/* Edit Dialog */}
        <VoterFileLoadedEditDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          data={selectedItem}
          onSave={handleSaveItem}
          isEditMode={true}
        />

        {/* View Dialog */}
        <VoterFileLoadedViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          data={selectedItem}
          onEdit={handleEditFromView}
        />

        {/* L&A Checklist Dialog for Voter File Loading */}
        <LAChecklistDialog
          open={showLADialog}
          onOpenChange={setShowLADialog}
          onSessionComplete={(session) => {
            // Refresh data after session completion
            const loadData = async () => {
              const results = await laChecklistService.getVoterFileLoadResults();
              setVoterFileData(results);
            };
            loadData();
            setShowLADialog(false);
          }}
        />
      </div>
    </AppLayout>
  );
};

export default SimpleVoterFileLoadedPage;
