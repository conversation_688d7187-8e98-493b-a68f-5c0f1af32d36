import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Filter, Search, Eye, Pencil, Plus } from "lucide-react";
import { PageTitle } from "@/components/layout/PageTitle";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { exportData } from "@/utils/exportUtils";
import { toast } from "@/components/ui/use-toast";
import { ScannerViewDialog } from "@/components/la-checklist/ScannerViewDialog";
import { ScannerEditDialog } from "@/components/la-checklist/ScannerEditDialog";
import { laChecklistService, LAChecklistAsset, LAChecklistSession } from "@/services/laChecklistService";
import { LAChecklistDialog } from "@/components/la-checklist/LAChecklistDialog";

const SimpleScannersPage = () => {
  // Backend data states
  const [scannerAssets, setScannerAssets] = useState<LAChecklistAsset[]>([]);
  const [scannerSessions, setScannerSessions] = useState<LAChecklistSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showLADialog, setShowLADialog] = useState(false);

  // Load scanner data from backend
  useEffect(() => {
    const loadScannerData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [assets, sessions] = await Promise.all([
          laChecklistService.getScannerAssets(),
          laChecklistService.getSessions()
        ]);

        setScannerAssets(assets);
        // Filter sessions for scanner assets only
        const scannerAssetIds = assets.map(a => a.id);
        setScannerSessions(sessions.filter(s => scannerAssetIds.includes(s.asset_id)));
      } catch (err) {
        console.error('Error loading scanner data:', err);
        setError('Failed to load scanner data. Please ensure the backend is running.');
      } finally {
        setLoading(false);
      }
    };

    loadScannerData();
  }, []);

  // Convert backend data to display format
  const scannerData = scannerAssets.map(asset => {
    const session = scannerSessions.find(s => s.asset_id === asset.id);
    return {
      id: asset.id,
      election: "Current Election",
      location: asset.location || "Unknown",
      bmd: asset.asset_id,
      date: session ? new Date(session.started_at).toLocaleDateString() : new Date().toLocaleDateString(),
      serialNumber: asset.serial_number || asset.asset_id,
      county: asset.county || "Unknown",
      status: session ? (session.status === 'Completed' ? session.overall_result || 'Completed' : session.status) : 'Pending',
      // Default checklist values - these would come from session details in a real implementation
      insertCompactFlash: true,
      powerOn: true,
      confirmSecurityKey: true,
      diagnosticsPassed: true,
      confirmVersion: true,
      openPolls: true,
      confirmPollingPlace: true,
      confirmZeroCount: true,
      scanTestDeck: true,
      closePolls: true,
      confirmPrintedResult: true,
      powerOff: true,
      removePollWorkerCard: true,
      insertPollWorkerCard: true,
      confirmSecurityKey2: true,
      reZeroAndPrint: true,
      confirmBallotBox: true,
      pollWorkerCard: "555",
      adminCFSeal: "123",
      ballotBoxSeal: "124",
      initials: "JD",
      technician: "System User",
      testDeck: "TD-2023-P-001"
    };
  });

  // State for dialogs and UI
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [statusFilter, setStatusFilter] = useState("all");
  const [countyFilter, setCountyFilter] = useState("all");
  const [showFilters, setShowFilters] = useState(false);

  // Apply filters to data
  const filteredData = scannerData.filter(scanner => {
    // Search term filter
    const matchesSearch = searchTerm === "" ||
      scanner.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      scanner.county.toLowerCase().includes(searchTerm.toLowerCase()) ||
      scanner.election.toLowerCase().includes(searchTerm.toLowerCase()) ||
      scanner.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      scanner.technician.toLowerCase().includes(searchTerm.toLowerCase());

    // Status filter
    const matchesStatus = statusFilter === "all" || scanner.status === statusFilter;

    // County filter
    const matchesCounty = countyFilter === "all" || scanner.county === countyFilter;

    return matchesSearch && matchesStatus && matchesCounty;
  });

  // Handle view item
  const handleViewItem = (item: any) => {
    console.log("View item clicked:", item);
    setSelectedItem({...item}); // Create a new object to ensure state updates
    setShowViewDialog(true);
  };

  // Handle edit item
  const handleEditItem = (item: any) => {
    console.log("Edit item clicked:", item);
    setSelectedItem({...item}); // Create a new object to ensure state updates
    setShowEditDialog(true);
  };

  // Handle add new item
  const handleAddItem = () => {
    setSelectedItem(null);
    setShowAddDialog(true);
  };

  // Handle save item (for both add and edit)
  const handleSaveItem = (data: any) => {
    if (data.id) {
      // Edit existing item
      setScannerData(scannerData.map(item =>
        item.id === data.id ? { ...data } : item
      ));
    } else {
      // Add new item
      const newItem = {
        ...data,
        id: scannerData.length > 0 ? Math.max(...scannerData.map(item => item.id)) + 1 : 1,
        serialNumber: `SN-SC-${scannerData.length + 1001}-A`,
        testDeck: "TD-2023-P-001",
        status: "Pending"
      };
      setScannerData([...scannerData, newItem]);
    }
  };

  // Handle edit from view dialog
  const handleEditFromView = () => {
    setShowViewDialog(false);
    setShowEditDialog(true);
  };

  // Handle export with filtered data
  const handleExport = (format: string) => {
    const exportDataArray = filteredData.map(scanner => ({
      id: `SC-${scanner.id.toString().padStart(4, '0')}`,
      serialNumber: scanner.serialNumber,
      county: scanner.county,
      election: scanner.election,
      location: scanner.location,
      testDate: scanner.date,
      technician: scanner.technician,
      status: scanner.status
    }));

    exportData(
      exportDataArray,
      format,
      "Scanner_LA_Testing_Units",
      "Scanner L&A Testing Units",
      ["id", "serialNumber", "county", "election", "location", "testDate", "technician", "status"]
    );

    // Show toast notification for copy action
    if (format === "copy") {
      toast({
        title: "Copied to clipboard",
        description: `${filteredData.length} scanner records copied`,
        type: "success"
      });
    }
  };

  // Get unique counties for filter dropdown
  const uniqueCounties = Array.from(new Set(scannerData.map(scanner => scanner.county)));

  return (
    <AppLayout>
      <div className="p-4 md:p-6 lg:p-8">
        <div className="mb-4">
          <Link to="/la-checklist">
            <Button variant="ghost" size="sm" className="mr-2">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to L&A Checklist
            </Button>
          </Link>
        </div>

        <PageTitle
          title="Scanner L&A Testing"
          description="Manage and track Logic & Accuracy testing for scanners"
          onExport={(format) => handleExport(format)}
          actions={
            <>
              <Button
                variant="outline"
                size="sm"
                className="mr-2"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                {showFilters ? "Hide Filters" : "Show Filters"}
              </Button>
              <Button size="sm" onClick={() => setShowLADialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                New Test Session
              </Button>
            </>
          }
        />

        {showFilters && (
          <div className="mb-6 border rounded-lg p-4 bg-gray-50">
            <h3 className="font-medium mb-3">Filter Scanner Units</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="search">Search</Label>
                <div className="flex mt-1">
                  <Input
                    id="search"
                    placeholder="Search by ID or S/N"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <Button className="ml-2" size="icon" variant="ghost">
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={statusFilter}
                  onValueChange={setStatusFilter}
                >
                  <SelectTrigger id="status" className="mt-1">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="Passed">Passed</SelectItem>
                    <SelectItem value="Failed">Failed</SelectItem>
                    <SelectItem value="Pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="county">County</Label>
                <Select
                  value={countyFilter}
                  onValueChange={setCountyFilter}
                >
                  <SelectTrigger id="county" className="mt-1">
                    <SelectValue placeholder="Select county" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Counties</SelectItem>
                    {uniqueCounties.map(county => (
                      <SelectItem key={county} value={county}>{county}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mt-4 flex justify-end">
              <Button
                variant="outline"
                size="sm"
                className="mr-2"
                onClick={() => {
                  setSearchTerm("");
                  setStatusFilter("all");
                  setCountyFilter("all");
                }}
              >
                Reset Filters
              </Button>
              <Button
                size="sm"
                onClick={() => handleExport("copy")}
              >
                Copy Filtered Results
              </Button>
            </div>
          </div>
        )}

        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Scanner Units</h2>
          <div className="text-sm text-gray-500">
            Showing {filteredData.length} of {scannerData.length} units
          </div>
        </div>

        <div className="border rounded-lg shadow-sm overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-4 font-medium">Scanner ID</th>
                <th className="text-left py-3 px-4 font-medium">Serial Number</th>
                <th className="text-left py-3 px-4 font-medium">County</th>
                <th className="text-left py-3 px-4 font-medium">Election</th>
                <th className="text-left py-3 px-4 font-medium">Location</th>
                <th className="text-left py-3 px-4 font-medium">Test Date</th>
                <th className="text-left py-3 px-4 font-medium">Status</th>
                <th className="text-left py-3 px-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.length > 0 ? (
                filteredData.map((scanner) => (
                  <tr key={scanner.id} className="border-t hover:bg-gray-50">
                    <td className="py-3 px-4">SC-{scanner.id.toString().padStart(4, '0')}</td>
                    <td className="py-3 px-4">{scanner.serialNumber}</td>
                    <td className="py-3 px-4">{scanner.county}</td>
                    <td className="py-3 px-4">{scanner.election}</td>
                    <td className="py-3 px-4">{scanner.location}</td>
                    <td className="py-3 px-4">{scanner.date}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        scanner.status === "Passed"
                          ? "bg-green-100 text-green-800"
                          : scanner.status === "Failed"
                            ? "bg-red-100 text-red-800"
                            : scanner.status === "Retest"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-gray-100 text-gray-800"
                      }`}>
                        {scanner.status}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm" onClick={() => handleViewItem(scanner)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleEditItem(scanner)}>
                          <Pencil className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={8} className="py-4 px-4 text-center text-gray-500">
                    No scanner units match the current filters
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* These dialogs are for editing existing Scanner records, not for L&A testing */}
        {/* Add Dialog */}
        <ScannerEditDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          data={null}
          onSave={handleSaveItem}
          isEditMode={false}
        />

        {/* Edit Dialog */}
        <ScannerEditDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          data={selectedItem}
          onSave={handleSaveItem}
          isEditMode={true}
        />

        {/* View Dialog */}
        <ScannerViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          data={selectedItem}
          onEdit={handleEditFromView}
        />

        {/* L&A Checklist Dialog for Scanners */}
        <LAChecklistDialog
          open={showLADialog}
          onOpenChange={setShowLADialog}
          onSessionComplete={(session) => {
            // Refresh data after session completion
            setScannerSessions(prev => [...prev, session]);
            setShowLADialog(false);
          }}
        />
      </div>
    </AppLayout>
  );
};

export default SimpleScannersPage;
