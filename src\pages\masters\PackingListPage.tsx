import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Plus, Edit, Trash, Eye, AlertTriangle, Download, FileText, Minus } from "lucide-react";
import { exportData } from "@/utils/exportUtils";
import { ExportOptions } from "@/components/ExportOptions";
import mastersConsumablesService, { MastersConsumable } from "@/services/mastersConsumablesService";
import locationService, { Location } from "@/services/locationService";

// Using the local storage structure that matches the screenshots
interface PackingListItem {
  id: string;
  name: string;
  itemNo: string;
  location: string;
  quantity: number;
}

interface PackingList {
  id: string;
  refNo: string;
  description: string;
  status: boolean; // true for ON, false for OFF
  image?: string;
  lastUpdatedBy?: string;
  lastUpdatedDate?: string;
  items: PackingListItem[];
}

interface PackingListCreationData {
  refNo: string;
  description: string;
  status: boolean;
  image?: string;
  items: PackingListItem[];
}

// Mock data for demonstration - replace with actual API calls
const mockPackingLists: PackingList[] = [
  {
    id: "1",
    refNo: "PL0001",
    description: "Consumable_Package_1",
    status: true,
    lastUpdatedBy: "Super Admin",
    lastUpdatedDate: "2025-06-08",
    items: [
      { id: "1", name: "AV Cables", itemNo: "001", location: "Compartment 1", quantity: 1000 },
      { id: "2", name: "ADP Supply Kit", itemNo: "002", location: "Compartment 2", quantity: 4000 },
      { id: "3", name: "Envelopes", itemNo: "003", location: "Compartment 3", quantity: 200 },
      { id: "4", name: "Printer Paper", itemNo: "004", location: "Main Storage", quantity: 200 }
    ]
  },
  {
    id: "2", 
    refNo: "PL0002",
    description: "Supply Package 2",
    status: true,
    lastUpdatedBy: "Super Admin",
    lastUpdatedDate: "2025-06-08",
    items: []
  },
  {
    id: "3",
    refNo: "PL0004", 
    description: "Supply package 3",
    status: true,
    lastUpdatedBy: "Super Admin",
    lastUpdatedDate: "2025-06-08",
    items: []
  },
  {
    id: "4",
    refNo: "PL0003",
    description: "Supply Package ?!",
    status: true,
    lastUpdatedBy: "Prakash Balasubramaniam",
    lastUpdatedDate: "2025-06-08",
    items: []
  },
  {
    id: "5",
    refNo: "PL0021",
    description: "Supply package 1",
    status: true,
    lastUpdatedBy: "Super Admin", 
    lastUpdatedDate: "2025-06-08",
    items: []
  }
];

// Add Dialog Component
function PackingListDialog({ 
  open, 
  onOpenChange, 
  packingList, 
  consumables,
  locations,
  onSave 
}: { 
  open: boolean; 
  onOpenChange: (open: boolean) => void; 
  packingList?: PackingList;
  consumables: MastersConsumable[];
  locations: Location[];
  onSave: (data: PackingListCreationData) => void; 
}) {
  const [formData, setFormData] = useState<PackingListCreationData>({
    refNo: '',
    description: '',
    status: true,
    items: []
  });

  const [newItem, setNewItem] = useState({
    consumableId: '',
    locationId: '',
    quantity: 1
  });

  useEffect(() => {
    if (packingList && open) {
      setFormData({
        refNo: packingList.refNo,
        description: packingList.description,
        status: packingList.status,
        image: packingList.image,
        items: [...packingList.items]
      });
    } else if (!packingList && open) {
      setFormData({
        refNo: '',
        description: '',
        status: true,
        items: []
      });
    }
    setNewItem({ consumableId: '', locationId: '', quantity: 1 });
  }, [packingList, open]);

  const handleAddItem = () => {
    console.log('Add item clicked!');
    console.log('Current newItem state:', newItem);
    console.log('Validation checks:', {
      hasConsumableId: !!newItem.consumableId,
      hasLocationId: !!newItem.locationId,
      hasValidQuantity: newItem.quantity > 0
    });
    
    if (newItem.consumableId && newItem.locationId && newItem.quantity > 0) {
      const selectedConsumable = consumables.find(c => c.id === newItem.consumableId);
      const selectedLocation = locations.find(l => l.id === newItem.locationId);
      
      console.log('Found consumable:', selectedConsumable);
      console.log('Found location:', selectedLocation);
      
      if (selectedConsumable && selectedLocation) {
        const item: PackingListItem = {
          id: Date.now().toString(),
          name: selectedConsumable.name,
          itemNo: selectedConsumable.itemNo,
          location: selectedLocation.name,
          quantity: newItem.quantity
        };
        
        console.log('Creating item:', item);
        console.log('Current formData items before add:', formData.items);
        
        setFormData(prev => {
          const updated = {
            ...prev,
            items: [...prev.items, item]
          };
          console.log('Updated formData:', updated);
          return updated;
        });
        
        setNewItem({ consumableId: '', locationId: '', quantity: 1 });
        console.log('Item added successfully!');
      } else {
        console.error('Could not find selected consumable or location');
      }
    } else {
      console.error('Validation failed - missing required fields');
    }
  };

  const handleRemoveItem = (itemId: string) => {
    setFormData({
      ...formData,
      items: formData.items.filter(item => item.id !== itemId)
    });
  };

  const handleUpdateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity > 0) {
      setFormData({
        ...formData,
        items: formData.items.map(item => 
          item.id === itemId ? { ...item, quantity: newQuantity } : item
        )
      });
    }
  };

  const handleSave = () => {
    console.log('Save clicked!');
    console.log('Current formData:', formData);
    console.log('Validation:', {
      hasRefNo: !!formData.refNo,
      hasDescription: !!formData.description,
      itemsCount: formData.items.length
    });
    
    if (formData.refNo && formData.description) {
      console.log('Validation passed, calling onSave...');
      onSave(formData);
    } else {
      console.error('Validation failed - missing ref no or description');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {packingList ? 'Edit Packing List' : 'Add New Packing List'}
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          {/* Basic Information */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="refNo" className="text-right">
              Ref No *
            </Label>
            <Input
              id="refNo"
              value={formData.refNo}
              onChange={(e) => setFormData({ ...formData, refNo: e.target.value })}
              className="col-span-3"
              placeholder="Enter reference number"
            />
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              Description *
            </Label>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="col-span-3"
              placeholder="Enter description"
            />
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Status *
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Switch
                id="status"
                checked={formData.status}
                onCheckedChange={(checked) => setFormData({ ...formData, status: checked })}
              />
              <span className="text-sm">{formData.status ? 'ON' : 'OFF'}</span>
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="image" className="text-right">
              Image
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Button variant="outline" size="sm">
                Choose File
              </Button>
              <span className="text-sm text-gray-500">No file chosen</span>
              <Button variant="outline" size="sm">
                Browse
              </Button>
            </div>
          </div>

          {/* Items Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Items</h3>
            
            {/* Add Item Form */}
            <div className="grid grid-cols-12 gap-2 items-end">
              <div className="col-span-4">
                <Label className="text-sm">Item</Label>
                <Select value={newItem.consumableId} onValueChange={(value) => setNewItem({...newItem, consumableId: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select an item" />
                  </SelectTrigger>
                  <SelectContent>
                    {consumables.length === 0 ? (
                      <SelectItem value="" disabled>Loading items...</SelectItem>
                    ) : (
                      consumables.map((consumable) => (
                        <SelectItem key={consumable.id} value={consumable.id}>
                          {consumable.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {/* Temporary debug info */}
                <div className="text-xs text-gray-500 mt-1">
                  Items available: {consumables.length}
                  {consumables.length > 0 && ` (${consumables.slice(0, 3).map(c => c.name).join(', ')}...)`}
                </div>
              </div>
              
              <div className="col-span-4">
                <Label className="text-sm">Location</Label>
                <Select value={newItem.locationId} onValueChange={(value) => setNewItem({...newItem, locationId: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a location" />
                  </SelectTrigger>
                  <SelectContent>
                    {locations.length === 0 ? (
                      <SelectItem value="" disabled>Loading locations...</SelectItem>
                    ) : (
                      locations.map((location) => (
                        <SelectItem key={location.id} value={location.id}>
                          {location.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {/* Temporary debug info */}
                <div className="text-xs text-gray-500 mt-1">
                  Locations available: {locations.length}
                  {locations.length > 0 && ` (${locations.slice(0, 3).map(l => l.name).join(', ')}...)`}
                </div>
              </div>
              
              <div className="col-span-2">
                <Label className="text-sm">Qty</Label>
                <Input
                  type="number"
                  min="1"
                  value={newItem.quantity}
                  onChange={(e) => setNewItem({...newItem, quantity: parseInt(e.target.value) || 1})}
                  placeholder="Qty"
                />
              </div>
              
              <div className="col-span-2">
                <Button onClick={handleAddItem} className="w-full">
                  <Plus className="h-4 w-4 mr-1" />
                  Add
                </Button>
              </div>
            </div>

            {/* Items Table */}
            {formData.items.length > 0 ? (
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item</TableHead>
                      <TableHead>Item No.</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Qty</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {formData.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell>{item.itemNo}</TableCell>
                        <TableCell>{item.location}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 max-w-32">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                              disabled={item.quantity <= 1}
                              className="h-8 w-8 p-0"
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <Input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={(e) => handleUpdateQuantity(item.id, parseInt(e.target.value) || 1)}
                              className="h-8 text-center"
                            />
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                              className="h-8 w-8 p-0"
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveItem(item.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 border rounded-lg">
                No items added yet. Add items using the form above.
              </div>
            )}
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={!formData.refNo || !formData.description}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// View Dialog Component
function PackingListViewDialog({ 
  open, 
  onOpenChange, 
  packingList 
}: { 
  open: boolean; 
  onOpenChange: (open: boolean) => void; 
  packingList?: PackingList; 
}) {
  if (!packingList) return null;

  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    const exportData = {
      'Ref No': packingList.refNo,
      'Description': packingList.description,
      'Status': packingList.status ? 'ON' : 'OFF',
      'Items': packingList.items.length,
      'Last Updated': packingList.lastUpdatedDate || 'N/A',
      'Last Updated By': packingList.lastUpdatedBy || 'N/A'
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${packingList.refNo}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-blue-600">
            {packingList.description}
          </DialogTitle>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handlePrint}>
              <FileText className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-blue-600">{packingList.description}</h3>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Ref No:</span> {packingList.refNo}
              </div>
              <div>
                <span className="font-medium">Status:</span>{" "}
                <span className={`font-medium ${packingList.status ? 'text-green-600' : 'text-red-600'}`}>
                  {packingList.status ? 'ON' : 'OFF'}
                </span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="font-medium">Items</h4>
            
            {packingList.items.length > 0 ? (
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead className="text-right">Qty</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {packingList.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell>{item.location}</TableCell>
                        <TableCell className="text-right">{item.quantity}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                No items in this packing list
              </div>
            )}
          </div>

          {packingList.lastUpdatedBy && (
            <div className="text-sm text-gray-500 text-right">
              Last updated by {packingList.lastUpdatedBy} on {packingList.lastUpdatedDate}
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Mock data for immediate use
const mockConsumables: MastersConsumable[] = [
  { id: '1', name: 'AV Cables', itemNo: '001', categoryId: '1', unit: 'pieces', minQty: 0, maxQty: 1000, unitCost: 10, status: true, isCritical: false, leadTimeDays: 7, properties: {}, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
  { id: '2', name: 'ADP Supply Kit', itemNo: '002', categoryId: '1', unit: 'pieces', minQty: 0, maxQty: 1000, unitCost: 25, status: true, isCritical: false, leadTimeDays: 7, properties: {}, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
  { id: '3', name: 'Envelopes', itemNo: '003', categoryId: '1', unit: 'pieces', minQty: 0, maxQty: 1000, unitCost: 5, status: true, isCritical: false, leadTimeDays: 7, properties: {}, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
  { id: '4', name: 'Printer Paper', itemNo: '004', categoryId: '1', unit: 'pieces', minQty: 0, maxQty: 1000, unitCost: 15, status: true, isCritical: false, leadTimeDays: 7, properties: {}, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
  { id: '5', name: 'Ballot Boxes', itemNo: '005', categoryId: '1', unit: 'pieces', minQty: 0, maxQty: 1000, unitCost: 50, status: true, isCritical: false, leadTimeDays: 7, properties: {}, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
  { id: '6', name: 'Security Seals', itemNo: '006', categoryId: '1', unit: 'pieces', minQty: 0, maxQty: 1000, unitCost: 2, status: true, isCritical: false, leadTimeDays: 7, properties: {}, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }
];

const mockLocations: Location[] = [
  ...Array.from({ length: 10 }, (_, i) => ({
    id: `comp-${i + 1}`,
    name: `Compartment ${i + 1}`,
    type: 'general' as const,
    status: 'Active',
    area: '',
    address: '',
    city: '',
    state: '',
    zip: '',
    contact_person: '',
    phone: '',
    email: '',
    addressLine2: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  })),
  { id: 'main-storage', name: 'Main Storage', type: 'general' as const, status: 'Active', area: '', address: '', city: '', state: '', zip: '', contact_person: '', phone: '', email: '', addressLine2: '', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
  { id: 'secondary-storage', name: 'Secondary Storage', type: 'general' as const, status: 'Active', area: '', address: '', city: '', state: '', zip: '', contact_person: '', phone: '', email: '', addressLine2: '', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
  { id: 'warehouse', name: 'Warehouse', type: 'general' as const, status: 'Active', area: '', address: '', city: '', state: '', zip: '', contact_person: '', phone: '', email: '', addressLine2: '', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }
];

export default function PackingListPage() {
  const [packingLists, setPackingLists] = useState<PackingList[]>(mockPackingLists);
  const [filteredPackingLists, setFilteredPackingLists] = useState<PackingList[]>(mockPackingLists);
  const [consumables, setConsumables] = useState<MastersConsumable[]>(mockConsumables);
  const [locations, setLocations] = useState<Location[]>(mockLocations);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPackingList, setSelectedPackingList] = useState<PackingList | undefined>(undefined);

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Load data from backend
  useEffect(() => {
    loadData();
  }, []);

  // Filter packing lists when search query changes
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredPackingLists(packingLists);
    } else {
      const lowercaseQuery = searchQuery.toLowerCase();
      const filtered = packingLists.filter(
        (packingList) =>
          packingList.refNo.toLowerCase().includes(lowercaseQuery) ||
          packingList.description.toLowerCase().includes(lowercaseQuery) ||
          packingList.lastUpdatedBy?.toLowerCase().includes(lowercaseQuery)
      );
      setFilteredPackingLists(filtered);
    }
    setCurrentPage(1);
  }, [searchQuery, packingLists]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Try to enhance with API data, but keep existing mock data as fallback
      try {
        const consumablesResult = await mastersConsumablesService.getAllConsumables({ limit: 100 });
        if (consumablesResult.consumables && consumablesResult.consumables.length > 0) {
          setConsumables(consumablesResult.consumables);
        }
      } catch (consumablesError) {
        // Keep existing mock data
      }
      
      try {
        const locationsResult = await locationService.getAllLocations();
        if (locationsResult.locations && locationsResult.locations.length > 0) {
          setLocations(locationsResult.locations);
        }
      } catch (locationsError) {
        // Keep existing mock data
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to enhance data from API');
    } finally {
      setLoading(false);
    }
  };

  // Handle adding a new packing list
  const handleAddPackingList = async (packingListData: PackingListCreationData) => {
    try {
      const newPackingList: PackingList = {
        id: Date.now().toString(),
        ...packingListData,
        lastUpdatedBy: "Current User", // Replace with actual user
        lastUpdatedDate: new Date().toISOString().split('T')[0]
      };
      
      setPackingLists([...packingLists, newPackingList]);
      setShowAddDialog(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add packing list');
      console.error('Error adding packing list:', err);
    }
  };

  // Handle editing a packing list
  const handleEditPackingList = async (packingListData: PackingListCreationData) => {
    if (selectedPackingList) {
      try {
        const updatedPackingList: PackingList = {
          ...selectedPackingList,
          ...packingListData,
          lastUpdatedBy: "Current User", // Replace with actual user
          lastUpdatedDate: new Date().toISOString().split('T')[0]
        };

        setPackingLists(packingLists.map(pl =>
          pl.id === updatedPackingList.id ? updatedPackingList : pl
        ));
        setShowEditDialog(false);
        setSelectedPackingList(undefined);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to update packing list');
        console.error('Error updating packing list:', err);
      }
    }
  };

  // Handle deleting a packing list
  const handleDeletePackingList = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this packing list?")) {
      try {
        setPackingLists(packingLists.filter(pl => pl.id !== id));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete packing list');
        console.error('Error deleting packing list:', err);
      }
    }
  };

  // Handle viewing a packing list
  const handleViewPackingList = (packingList: PackingList) => {
    setSelectedPackingList(packingList);
    setShowViewDialog(true);
  };

  // Handle opening the edit dialog
  const handleEditClick = (packingList: PackingList) => {
    setSelectedPackingList(packingList);
    setShowEditDialog(true);
  };

  // Handle export
  const handleExport = (format: string) => {
    const exportableData = filteredPackingLists.map(pl => ({
      'Ref No': pl.refNo,
      'Description': pl.description,
      'Status': pl.status ? 'ON' : 'OFF',
      'Items Count': pl.items.length,
      'Last Updated By': pl.lastUpdatedBy || 'N/A',
      'Last Updated Date': pl.lastUpdatedDate || 'N/A'
    }));

    exportData(
      exportableData,
      format,
      'Packing_Lists_Export',
      'Packing Lists',
      ['Ref No', 'Description', 'Status', 'Items Count', 'Last Updated By', 'Last Updated Date']
    );
  };

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredPackingLists.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredPackingLists.length / itemsPerPage);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  if (loading) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <p>Loading packing lists...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600">{error}</p>
              <Button onClick={loadData} className="mt-4">
                Retry
              </Button>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="p-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Packing List</h1>
          <div className="flex gap-2">
            <ExportOptions onExport={handleExport} />
            <Button onClick={() => setShowAddDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add New
            </Button>
          </div>
        </div>

        <div className="flex items-center mb-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Ref No</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Last Updated By</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentItems.map((packingList) => (
                  <TableRow key={packingList.id} className="cursor-pointer hover:bg-gray-50" onClick={() => handleViewPackingList(packingList)}>
                    <TableCell className="font-medium">{packingList.refNo}</TableCell>
                    <TableCell>{packingList.description}</TableCell>
                    <TableCell>{packingList.lastUpdatedBy || 'N/A'}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2" onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="sm" onClick={() => handleViewPackingList(packingList)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(packingList)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleDeletePackingList(packingList.id)}>
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {currentItems.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8 text-gray-500">
                      No packing lists found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center space-x-2">
            <Button
              variant="outline"
              onClick={() => paginate(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            {Array.from({ length: totalPages }, (_, i) => (
              <Button
                key={i + 1}
                variant={currentPage === i + 1 ? "default" : "outline"}
                onClick={() => paginate(i + 1)}
              >
                {i + 1}
              </Button>
            ))}
            <Button
              variant="outline"
              onClick={() => paginate(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}

        {/* Add Dialog */}
        <PackingListDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          consumables={consumables}
          locations={locations}
          onSave={handleAddPackingList}
        />

        {/* Edit Dialog */}
        <PackingListDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          packingList={selectedPackingList}
          consumables={consumables}
          locations={locations}
          onSave={handleEditPackingList}
        />

        {/* View Dialog */}
        <PackingListViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          packingList={selectedPackingList}
        />
      </div>
    </AppLayout>
  );
}
