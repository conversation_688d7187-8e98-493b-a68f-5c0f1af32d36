import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, FileDown, Edit, Eye, Trash, AlertTriangle } from "lucide-react";
import { exportData } from "@/utils/exportUtils";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";

// Simple interface for Rolling Cage that matches the UI
interface RollingCage {
  id: string;
  refNo: string;
  description: string;
  status: string;
  lastUpdatedBy: string;
  lastUpdatedDate: string;
  items: any[];
}

// Mock data for immediate functionality
const mockRollingCages: RollingCage[] = [
  {
    id: "1",
    refNo: "RC001",
    description: "Rolling Cage 1",
    status: "Available",
    lastUpdatedBy: "Super Admin",
    lastUpdatedDate: "2024-01-15",
    items: []
  },
  {
    id: "2",
    refNo: "RC002", 
    description: "Rolling Cage 2",
    status: "In Use",
    lastUpdatedBy: "Admin User",
    lastUpdatedDate: "2024-01-14",
    items: []
  },
  {
    id: "3",
    refNo: "RC003",
    description: "Rolling Cage 3", 
    status: "Maintenance",
    lastUpdatedBy: "Tech Support",
    lastUpdatedDate: "2024-01-13",
    items: []
  }
];

export default function RollingCagePage() {
  const [rollingCages, setRollingCages] = useState<RollingCage[]>(mockRollingCages);
  const [filteredRollingCages, setFilteredRollingCages] = useState<RollingCage[]>(mockRollingCages);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedRollingCage, setSelectedRollingCage] = useState<RollingCage | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Handle search
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredRollingCages(rollingCages || []);
    } else {
      const lowercasedQuery = searchQuery.toLowerCase();
      const filtered = (rollingCages || []).filter(
        (cage) =>
          cage.refNo?.toLowerCase().includes(lowercasedQuery) ||
          cage.description?.toLowerCase().includes(lowercasedQuery)
      );
      setFilteredRollingCages(filtered);
    }
    setCurrentPage(1);
  }, [searchQuery, rollingCages]);

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = (filteredRollingCages || []).slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil((filteredRollingCages || []).length / itemsPerPage);

  const paginate = (pageNumber: number) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  // Handle delete rolling cage
  const handleDeleteRollingCage = async () => {
    if (selectedRollingCage) {
      try {
        setRollingCages((rollingCages || []).filter(
          (cage) => cage.id !== selectedRollingCage.id
        ));
        setShowDeleteDialog(false);
        setSelectedRollingCage(undefined);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete rolling cage');
        console.error('Error deleting rolling cage:', err);
      }
    }
  };

  // Handle export
  const handleExport = (format: string) => {
    const exportableData = (filteredRollingCages || []).map(cage => ({
      'Ref No': cage.refNo,
      'Description': cage.description,
      'Status': cage.status,
      'Last Updated By': cage.lastUpdatedBy,
      'Last Updated Date': cage.lastUpdatedDate,
      'Items Count': cage.items.length
    }));

    exportData(
      exportableData,
      format,
      'Rolling_Cages_Export',
      'Rolling Cages List',
      ['Ref No', 'Description', 'Status', 'Last Updated By', 'Last Updated Date', 'Items Count']
    );
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <p>Loading rolling cages...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600">{error}</p>
              <Button onClick={() => setError(null)} className="mt-4">
                Retry
              </Button>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="p-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Rolling Cage Management</h1>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button size="sm" onClick={() => alert('Add functionality coming soon!')}>
              <Plus className="h-4 w-4 mr-2" />
              Add Rolling Cage
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Rolling Cages</CardTitle>
              <div className="relative w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Ref No</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Updated By</TableHead>
                  <TableHead>Last Updated Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentItems.length > 0 ? (
                  currentItems.map((cage) => (
                    <TableRow key={cage.id}>
                      <TableCell className="font-medium">{cage.refNo}</TableCell>
                      <TableCell>{cage.description}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          cage.status === 'Available' ? 'bg-green-100 text-green-800' :
                          cage.status === 'In Use' ? 'bg-blue-100 text-blue-800' :
                          cage.status === 'Maintenance' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {cage.status}
                        </span>
                      </TableCell>
                      <TableCell>{cage.lastUpdatedBy}</TableCell>
                      <TableCell>{cage.lastUpdatedDate}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => alert('View functionality coming soon!')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => alert('Edit functionality coming soon!')}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedRollingCage(cage);
                              setShowDeleteDialog(true);
                            }}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      No rolling cages found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center gap-2 mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => paginate(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                {Array.from({ length: totalPages }, (_, i) => (
                  <Button
                    key={i + 1}
                    variant={currentPage === i + 1 ? "default" : "outline"}
                    size="sm"
                    onClick={() => paginate(i + 1)}
                  >
                    {i + 1}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => paginate(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Rolling Cage</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this rolling cage? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteRollingCage}>
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AppLayout>
  );
}
