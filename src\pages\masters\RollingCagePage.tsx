import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, FileDown, Edit, Eye, Trash, AlertTriangle } from "lucide-react";
import { exportData } from "@/utils/exportUtils";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import rollingCageService, { RollingCage, RollingCageCreationData, RollingCageUpdateData } from "@/services/rollingCageService";

// Mock data as fallback when backend is not available
const mockRollingCages: RollingCage[] = [
  {
    id: "1",
    election: "General Election 2024",
    to: "Precinct 001",
    packing_date: "2024-01-15T09:00:00Z",
    packing_user: "Super Admin",
    supply_package: "Standard Package",
    type: "Election Supplies",
    location: "Warehouse A",
    quantity: 50,
    packed: true,
    ref_no: "RC001",
    description: "Rolling Cage for Precinct 001",
    proofed_by: "Admin User",
    unpacked: false,
    created_at: "2024-01-15T08:00:00Z",
    updated_at: "2024-01-15T09:00:00Z"
  },
  {
    id: "2",
    election: "General Election 2024",
    to: "Precinct 002",
    packing_date: "2024-01-14T10:00:00Z",
    packing_user: "Admin User",
    supply_package: "Enhanced Package",
    type: "Election Supplies",
    location: "Warehouse B",
    quantity: 75,
    packed: true,
    ref_no: "RC002",
    description: "Rolling Cage for Precinct 002",
    proofed_by: "Super Admin",
    unpacked: false,
    created_at: "2024-01-14T09:00:00Z",
    updated_at: "2024-01-14T10:00:00Z"
  },
  {
    id: "3",
    election: "General Election 2024",
    to: "Precinct 003",
    packing_date: "2024-01-13T11:00:00Z",
    packing_user: "Tech Support",
    supply_package: "Basic Package",
    type: "Maintenance Supplies",
    location: "Warehouse C",
    quantity: 25,
    packed: false,
    ref_no: "RC003",
    description: "Rolling Cage for Precinct 003",
    proofed_by: null,
    unpacked: false,
    created_at: "2024-01-13T10:00:00Z",
    updated_at: "2024-01-13T11:00:00Z"
  }
];

export default function RollingCagePage() {
  const [rollingCages, setRollingCages] = useState<RollingCage[]>([]);
  const [filteredRollingCages, setFilteredRollingCages] = useState<RollingCage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedRollingCage, setSelectedRollingCage] = useState<RollingCage | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // Load rolling cages from backend
  useEffect(() => {
    loadRollingCages();
  }, [currentPage, searchQuery]);

  // Handle search
  useEffect(() => {
    // Reset to first page when search changes
    if (currentPage !== 1) {
      setCurrentPage(1);
    } else {
      loadRollingCages();
    }
  }, [searchQuery]);

  const loadRollingCages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('Loading rolling cages from backend...');
      
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        search: searchQuery.trim() || undefined,
        sortBy: 'created_at',
        sortOrder: 'DESC'
      };

      const response = await rollingCageService.getAllRollingCages(params);
      
      if (response.success && response.data) {
        const cagesData = response.data.rollingCages || [];
        console.log('Loaded rolling cages from backend:', cagesData.length);
        
        setRollingCages(cagesData);
        setFilteredRollingCages(cagesData);
        
        if (response.data.pagination) {
          setTotalPages(response.data.pagination.totalPages);
          setTotalItems(response.data.pagination.totalItems);
        }
      } else {
        throw new Error('Invalid response format');
      }
      
    } catch (err) {
      console.error('Backend connection failed, using mock data:', err);
      setError(`Backend connection failed: ${err instanceof Error ? err.message : 'Unknown error'}. Using local data.`);
      
      // Use mock data as fallback
      let filteredMockData = mockRollingCages;
      
      if (searchQuery.trim()) {
        const lowercasedQuery = searchQuery.toLowerCase();
        filteredMockData = mockRollingCages.filter(
          (cage) =>
            cage.ref_no?.toLowerCase().includes(lowercasedQuery) ||
            cage.election?.toLowerCase().includes(lowercasedQuery) ||
            cage.to?.toLowerCase().includes(lowercasedQuery) ||
            cage.description?.toLowerCase().includes(lowercasedQuery)
        );
      }
      
      setRollingCages(filteredMockData);
      setFilteredRollingCages(filteredMockData);
      setTotalPages(Math.ceil(filteredMockData.length / itemsPerPage));
      setTotalItems(filteredMockData.length);
    } finally {
      setLoading(false);
    }
  };

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredRollingCages.slice(indexOfFirstItem, indexOfLastItem);

  const paginate = (pageNumber: number) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  // Handle delete rolling cage
  const handleDeleteRollingCage = async () => {
    if (selectedRollingCage) {
      try {
        console.log('Deleting rolling cage:', selectedRollingCage.id);
        
        const success = await rollingCageService.deleteRollingCage(selectedRollingCage.id);
        
        if (success) {
          console.log('Rolling cage deleted successfully');
          // Reload data from backend
          await loadRollingCages();
        } else {
          throw new Error('Delete operation failed');
        }
        
        setShowDeleteDialog(false);
        setSelectedRollingCage(undefined);
      } catch (err) {
        console.error('Delete failed, removing from local state:', err);
        // Fallback: remove from local state
        setRollingCages((rollingCages || []).filter(
          (cage) => cage.id !== selectedRollingCage.id
        ));
        setFilteredRollingCages((filteredRollingCages || []).filter(
          (cage) => cage.id !== selectedRollingCage.id
        ));
        setShowDeleteDialog(false);
        setSelectedRollingCage(undefined);
        setError(`Delete failed: ${err instanceof Error ? err.message : 'Unknown error'}. Removed from local view.`);
      }
    }
  };

  // Handle export
  const handleExport = (format: string) => {
    const exportableData = rollingCageService.prepareRollingCagesForExport(filteredRollingCages || []);

    exportData(
      exportableData,
      format,
      'Rolling_Cages_Export',
      'Rolling Cages List',
      ['ID', 'Ref No', 'Election', 'To', 'Description', 'Type', 'Location', 'Quantity', 'Packed', 'Unpacked', 'Packing Date', 'Packing User', 'Proofed By', 'Supply Package', 'Created At', 'Updated At']
    );
  };

  const getStatusBadge = (cage: RollingCage) => {
    if (cage.unpacked) {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Unpacked</span>;
    } else if (cage.packed) {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Packed</span>;
    } else {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Not Packed</span>;
    }
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <p>Loading rolling cages...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="p-6 space-y-6">
        {error && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
              <p className="text-yellow-800 text-sm">{error}</p>
              <Button 
                onClick={() => {
                  setError(null);
                  loadRollingCages();
                }} 
                variant="outline" 
                size="sm" 
                className="ml-auto"
              >
                Retry Connection
              </Button>
            </div>
          </div>
        )}

        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Rolling Cage Management</h1>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button size="sm" onClick={() => alert('Add functionality will be implemented with a proper form dialog')}>
              <Plus className="h-4 w-4 mr-2" />
              Add Rolling Cage
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Rolling Cages ({totalItems} total)</CardTitle>
              <div className="relative w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search cages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Ref No</TableHead>
                  <TableHead>Election</TableHead>
                  <TableHead>To</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentItems.length > 0 ? (
                  currentItems.map((cage) => (
                    <TableRow key={cage.id}>
                      <TableCell className="font-medium">{cage.ref_no || cage.id.slice(0, 8)}</TableCell>
                      <TableCell>{cage.election}</TableCell>
                      <TableCell>{cage.to}</TableCell>
                      <TableCell>{cage.description || 'N/A'}</TableCell>
                      <TableCell>{cage.type || 'N/A'}</TableCell>
                      <TableCell>{cage.quantity || 0}</TableCell>
                      <TableCell>{getStatusBadge(cage)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => alert(`View details for ${cage.ref_no || cage.id}`)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => alert(`Edit ${cage.ref_no || cage.id} - Form will be implemented`)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedRollingCage(cage);
                              setShowDeleteDialog(true);
                            }}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                      {searchQuery ? 'No rolling cages found matching your search' : 'No rolling cages found'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center gap-2 mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => paginate(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <Button
                      key={pageNum}
                      variant={currentPage === pageNum ? "default" : "outline"}
                      size="sm"
                      onClick={() => paginate(pageNum)}
                    >
                      {pageNum}
                    </Button>
                  );
                })}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => paginate(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Rolling Cage</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete rolling cage "{selectedRollingCage?.ref_no || selectedRollingCage?.id}"? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteRollingCage}>
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AppLayout>
  );
}
