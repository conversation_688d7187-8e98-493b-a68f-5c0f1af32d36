// L&A Checklist Service - Connects to backend API
import { apiCall } from '@/utils/api';

export interface LAChecklistAsset {
  id: number;
  asset_id: string;
  type: string;
  model: string;
  name: string;
  status: string;
  location: string;
  serial_number?: string;
  county?: string;
  state?: string;
  precinct?: string;
}

export interface LAChecklistItem {
  id: number;
  session_id: number;
  item_name: string;
  item_description?: string;
  is_required: boolean;
  status: 'Pending' | 'Pass' | 'Fail' | 'Skipped';
  checked_by?: number;
  comments?: string;
  checked_at?: string;
  created_at: string;
  updated_at: string;
}

export interface LAChecklistSession {
  id: number;
  session_id: string;
  asset_id: number;
  created_by: number;
  status: 'Active' | 'Completed';
  overall_result?: string;
  notes?: string;
  started_at: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
  checklist_items: LAChecklistItem[];
}

export interface LAChecklistSessionStart {
  asset_id: number;
  notes?: string;
  custom_items?: Array<{
    item_name: string;
    item_description?: string;
    is_required: boolean;
  }>;
}

export interface LAChecklistItemUpdate {
  status?: 'Pending' | 'Pass' | 'Fail' | 'Skipped';
  comments?: string;
}

// Pollpad specific testing interfaces
export interface VoterCardTestResult {
  id: number;
  session_id: string;
  asset_id: number;
  election: string;
  location: string;
  pollPad: string;
  precinctId: string;
  ballotType: string;
  combo: string;
  activationCode: string;
  note?: string;
  date: string;
  completedBy: string;
  status: 'Pass' | 'Fail' | 'Pending';
  created_at: string;
  updated_at: string;
}

export interface VoterFileLoadResult {
  id: number;
  session_id: string;
  asset_id: number;
  election: string;
  location: string;
  pollPad: string;
  fileVersion: string;
  loadStatus: 'Loaded' | 'Failed' | 'Pending';
  voterCount: string;
  note?: string;
  date: string;
  completedBy: string;
  status: 'Pass' | 'Fail' | 'Pending';
  created_at: string;
  updated_at: string;
}

class LAChecklistService {
  private baseUrl = '/api/la-checklist';

  async getAvailableAssets(): Promise<LAChecklistAsset[]> {
    try {
      const response = await apiCall<LAChecklistAsset[]>(`${this.baseUrl}/available-assets`);
      // The backend returns the array directly, not wrapped in a response object
      return Array.isArray(response) ? response : (response.data || []);
    } catch (error) {
      console.error('Error fetching available assets:', error);
      throw error;
    }
  }

  // Get assets by equipment type for specific L&A pages
  async getAssetsByType(equipmentType: string): Promise<LAChecklistAsset[]> {
    try {
      const allAssets = await this.getAvailableAssets();
      // Filter assets by type (case-insensitive)
      return allAssets.filter(asset =>
        asset.type.toLowerCase().includes(equipmentType.toLowerCase()) ||
        asset.name.toLowerCase().includes(equipmentType.toLowerCase())
      );
    } catch (error) {
      console.error(`Error fetching ${equipmentType} assets:`, error);
      throw error;
    }
  }

  // Specific methods for each equipment type
  async getPollpadAssets(): Promise<LAChecklistAsset[]> {
    return this.getAssetsByType('pollpad');
  }

  async getScannerAssets(): Promise<LAChecklistAsset[]> {
    return this.getAssetsByType('scanner');
  }

  async getBMDAssets(): Promise<LAChecklistAsset[]> {
    return this.getAssetsByType('bmd');
  }

  // Pollpad specific testing methods
  async getVoterCardTestResults(): Promise<VoterCardTestResult[]> {
    try {
      // For now, we'll create test results from L&A sessions for pollpads
      // In a real implementation, this would be a separate endpoint
      const [pollpadAssets, sessions] = await Promise.all([
        this.getPollpadAssets(),
        this.getSessions()
      ]);

      const pollpadAssetIds = pollpadAssets.map(a => a.id);
      const pollpadSessions = sessions.filter(s => pollpadAssetIds.includes(s.asset_id));

      return pollpadSessions.map((session, index) => {
        const asset = pollpadAssets.find(a => a.id === session.asset_id);
        return {
          id: session.id,
          session_id: session.session_id,
          asset_id: session.asset_id,
          election: "Current Election",
          location: asset?.location || "Unknown",
          pollPad: asset?.asset_id || "Unknown",
          precinctId: asset?.precinct || "001",
          ballotType: "Standard",
          combo: `${asset?.precinct || "001"}-STD`,
          activationCode: `AC${session.id.toString().padStart(5, '0')}`,
          note: session.notes || "",
          date: new Date(session.started_at).toLocaleDateString(),
          completedBy: "System User",
          status: session.status === 'Completed' ? (session.overall_result === 'PASS' ? 'Pass' : 'Fail') : 'Pending',
          created_at: session.created_at,
          updated_at: session.updated_at
        };
      });
    } catch (error) {
      console.error('Error fetching voter card test results:', error);
      throw error;
    }
  }

  async getVoterFileLoadResults(): Promise<VoterFileLoadResult[]> {
    try {
      // For now, we'll create file load results from L&A sessions for pollpads
      // In a real implementation, this would be a separate endpoint
      const [pollpadAssets, sessions] = await Promise.all([
        this.getPollpadAssets(),
        this.getSessions()
      ]);

      const pollpadAssetIds = pollpadAssets.map(a => a.id);
      const pollpadSessions = sessions.filter(s => pollpadAssetIds.includes(s.asset_id));

      return pollpadSessions.map((session, index) => {
        const asset = pollpadAssets.find(a => a.id === session.asset_id);
        return {
          id: session.id,
          session_id: session.session_id,
          asset_id: session.asset_id,
          election: "Current Election",
          location: asset?.location || "Unknown",
          pollPad: asset?.asset_id || "Unknown",
          fileVersion: `VF-2024-P-${session.id.toString().padStart(3, '0')}`,
          loadStatus: session.status === 'Completed' ? (session.overall_result === 'PASS' ? 'Loaded' : 'Failed') : 'Pending',
          voterCount: "250,000", // This would come from actual voter file data
          note: session.notes || "",
          date: new Date(session.started_at).toLocaleDateString(),
          completedBy: "System User",
          status: session.status === 'Completed' ? (session.overall_result === 'PASS' ? 'Pass' : 'Fail') : 'Pending',
          created_at: session.created_at,
          updated_at: session.updated_at
        };
      });
    } catch (error) {
      console.error('Error fetching voter file load results:', error);
      throw error;
    }
  }

  async getSessions(): Promise<LAChecklistSession[]> {
    try {
      const response = await apiCall<LAChecklistSession[]>(`${this.baseUrl}/sessions`);
      // The backend returns the array directly, not wrapped in a response object
      return Array.isArray(response) ? response : (response.data || []);
    } catch (error) {
      console.error('Error fetching L&A sessions:', error);
      throw error;
    }
  }

  async getSession(sessionId: string): Promise<LAChecklistSession> {
    try {
      const response = await apiCall(`${this.baseUrl}/sessions/${sessionId}`);
      if (response.success) {
        return response.data || response; // Handle both wrapped and direct responses
      }
      throw new Error(response.message || 'Failed to fetch L&A session');
    } catch (error) {
      console.error('Error fetching L&A session:', error);
      throw error;
    }
  }

  async startSession(sessionData: LAChecklistSessionStart): Promise<LAChecklistSession> {
    try {
      const response = await apiCall(`${this.baseUrl}/sessions`, {
        method: 'POST',
        body: JSON.stringify(sessionData),
      });
      if (response.success) {
        return response.data || response; // Handle both wrapped and direct responses
      }
      throw new Error(response.message || 'Failed to start L&A session');
    } catch (error) {
      console.error('Error starting L&A session:', error);
      throw error;
    }
  }

  async updateChecklistItem(itemId: number, updateData: LAChecklistItemUpdate): Promise<LAChecklistItem> {
    try {
      const response = await apiCall(`${this.baseUrl}/items/${itemId}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
      });
      if (response.success) {
        return response.data || response; // Handle both wrapped and direct responses
      }
      throw new Error(response.message || 'Failed to update checklist item');
    } catch (error) {
      console.error('Error updating checklist item:', error);
      throw error;
    }
  }

  async completeSession(sessionId: string, notes?: string): Promise<LAChecklistSession> {
    try {
      const response = await apiCall(`${this.baseUrl}/sessions/${sessionId}/complete`, {
        method: 'POST',
        body: notes ? JSON.stringify({ notes }) : undefined,
      });
      if (response.success) {
        return response.data || response; // Handle both wrapped and direct responses
      }
      throw new Error(response.message || 'Failed to complete L&A session');
    } catch (error) {
      console.error('Error completing L&A session:', error);
      throw error;
    }
  }
}

export const laChecklistService = new LAChecklistService();
