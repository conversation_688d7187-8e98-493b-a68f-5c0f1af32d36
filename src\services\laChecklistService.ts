// L&A Checklist Service - Connects to backend API
import { apiCall } from '@/utils/api';

export interface LAChecklistAsset {
  id: number;
  asset_id: string;
  type: string;
  model: string;
  name: string;
  status: string;
  location: string;
}

export interface LAChecklistItem {
  id: number;
  session_id: number;
  item_name: string;
  item_description?: string;
  is_required: boolean;
  status: 'Pending' | 'Pass' | 'Fail' | 'Skipped';
  checked_by?: number;
  comments?: string;
  checked_at?: string;
  created_at: string;
  updated_at: string;
}

export interface LAChecklistSession {
  id: number;
  session_id: string;
  asset_id: number;
  created_by: number;
  status: 'Active' | 'Completed';
  overall_result?: string;
  notes?: string;
  started_at: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
  checklist_items: LAChecklistItem[];
}

export interface LAChecklistSessionStart {
  asset_id: number;
  notes?: string;
  custom_items?: Array<{
    item_name: string;
    item_description?: string;
    is_required: boolean;
  }>;
}

export interface LAChecklistItemUpdate {
  status?: 'Pending' | 'Pass' | 'Fail' | 'Skipped';
  comments?: string;
}

class LAChecklistService {
  private baseUrl = '/api/la-checklist';

  async getAvailableAssets(): Promise<LAChecklistAsset[]> {
    try {
      const response = await apiCall<LAChecklistAsset[]>(`${this.baseUrl}/available-assets`);
      // The backend returns the array directly, not wrapped in a response object
      return Array.isArray(response) ? response : (response.data || []);
    } catch (error) {
      console.error('Error fetching available assets:', error);
      throw error;
    }
  }

  async getSessions(): Promise<LAChecklistSession[]> {
    try {
      const response = await apiCall<LAChecklistSession[]>(`${this.baseUrl}/sessions`);
      // The backend returns the array directly, not wrapped in a response object
      return Array.isArray(response) ? response : (response.data || []);
    } catch (error) {
      console.error('Error fetching L&A sessions:', error);
      throw error;
    }
  }

  async getSession(sessionId: string): Promise<LAChecklistSession> {
    try {
      const response = await apiCall(`${this.baseUrl}/sessions/${sessionId}`);
      if (response.success) {
        return response.data || response; // Handle both wrapped and direct responses
      }
      throw new Error(response.message || 'Failed to fetch L&A session');
    } catch (error) {
      console.error('Error fetching L&A session:', error);
      throw error;
    }
  }

  async startSession(sessionData: LAChecklistSessionStart): Promise<LAChecklistSession> {
    try {
      const response = await apiCall(`${this.baseUrl}/sessions`, {
        method: 'POST',
        body: JSON.stringify(sessionData),
      });
      if (response.success) {
        return response.data || response; // Handle both wrapped and direct responses
      }
      throw new Error(response.message || 'Failed to start L&A session');
    } catch (error) {
      console.error('Error starting L&A session:', error);
      throw error;
    }
  }

  async updateChecklistItem(itemId: number, updateData: LAChecklistItemUpdate): Promise<LAChecklistItem> {
    try {
      const response = await apiCall(`${this.baseUrl}/items/${itemId}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
      });
      if (response.success) {
        return response.data || response; // Handle both wrapped and direct responses
      }
      throw new Error(response.message || 'Failed to update checklist item');
    } catch (error) {
      console.error('Error updating checklist item:', error);
      throw error;
    }
  }

  async completeSession(sessionId: string, notes?: string): Promise<LAChecklistSession> {
    try {
      const response = await apiCall(`${this.baseUrl}/sessions/${sessionId}/complete`, {
        method: 'POST',
        body: notes ? JSON.stringify({ notes }) : undefined,
      });
      if (response.success) {
        return response.data || response; // Handle both wrapped and direct responses
      }
      throw new Error(response.message || 'Failed to complete L&A session');
    } catch (error) {
      console.error('Error completing L&A session:', error);
      throw error;
    }
  }
}

export const laChecklistService = new LAChecklistService();
