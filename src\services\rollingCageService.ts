// Rolling Cage Service - Connects to FastAPI backend
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface RollingCage {
  id: string;
  election: string;
  to: string;
  packing_date: string;
  packing_user?: string;
  supply_package?: string;
  type?: string;
  location?: string;
  quantity?: number;
  packed: boolean;
  ref_no?: string;
  description?: string;
  proofed_by?: string;
  unpacked: boolean;
  created_at: string;
  updated_at: string;
}

export interface RollingCageCreationData {
  election: string;
  to: string;
  packing_date: string;
  packing_user?: string;
  supply_package?: string;
  type?: string;
  location?: string;
  quantity?: number;
  packed?: boolean;
  ref_no?: string;
  description?: string;
  proofed_by?: string;
  unpacked?: boolean;
}

export interface RollingCageUpdateData {
  election?: string;
  to?: string;
  packing_date?: string;
  packing_user?: string;
  supply_package?: string;
  type?: string;
  location?: string;
  quantity?: number;
  packed?: boolean;
  ref_no?: string;
  description?: string;
  proofed_by?: string;
  unpacked?: boolean;
}

export interface RollingCageResponse {
  success: boolean;
  data: {
    rollingCages: RollingCage[];
    pagination?: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      itemsPerPage: number;
    };
  };
}

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

class RollingCageService {
  // Get all rolling cages
  async getAllRollingCages(params?: {
    page?: number;
    limit?: number;
    search?: string;
    election?: string;
    to?: string;
    packed?: boolean;
    unpacked?: boolean;
    type?: string;
    location?: string;
    sortBy?: string;
    sortOrder?: string;
  }): Promise<RollingCageResponse> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.election) queryParams.append('election', params.election);
      if (params?.to) queryParams.append('to', params.to);
      if (params?.packed !== undefined) queryParams.append('packed', params.packed.toString());
      if (params?.unpacked !== undefined) queryParams.append('unpacked', params.unpacked.toString());
      if (params?.type) queryParams.append('type', params.type);
      if (params?.location) queryParams.append('location', params.location);
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const queryString = queryParams.toString();
      const endpoint = queryString ? `/rolling-cage/?${queryString}` : `/rolling-cage/`;
      const response = await apiCall(endpoint);
      
      if (response.success) {
        return response;
      }
      throw new Error(response.message || 'Failed to fetch rolling cages');
    } catch (error) {
      console.error('Error fetching rolling cages:', error);
      throw error;
    }
  }

  // Get rolling cage by ID
  async getRollingCageById(id: string): Promise<RollingCage> {
    try {
      const response = await apiCall(`/rolling-cage/${id}`);
      if (response.success) {
        return response.data.rollingCage;
      }
      throw new Error(response.message || 'Failed to fetch rolling cage');
    } catch (error) {
      console.error('Error fetching rolling cage by ID:', error);
      throw error;
    }
  }

  // Create new rolling cage
  async createRollingCage(cageData: RollingCageCreationData): Promise<RollingCage> {
    try {
      const response = await apiCall('/rolling-cage/', {
        method: 'POST',
        body: JSON.stringify(cageData),
      });
      if (response.success) {
        return response.data.rollingCage;
      }
      throw new Error(response.message || 'Failed to create rolling cage');
    } catch (error) {
      console.error('Error creating rolling cage:', error);
      throw error;
    }
  }

  // Update rolling cage
  async updateRollingCage(id: string, cageData: RollingCageUpdateData): Promise<RollingCage> {
    try {
      const response = await apiCall(`/rolling-cage/${id}`, {
        method: 'PUT',
        body: JSON.stringify(cageData),
      });
      if (response.success) {
        return response.data.rollingCage;
      }
      throw new Error(response.message || 'Failed to update rolling cage');
    } catch (error) {
      console.error('Error updating rolling cage:', error);
      throw error;
    }
  }

  // Delete rolling cage
  async deleteRollingCage(id: string): Promise<boolean> {
    try {
      const response = await apiCall(`/rolling-cage/${id}`, {
        method: 'DELETE',
      });
      return response.success;
    } catch (error) {
      console.error('Error deleting rolling cage:', error);
      throw error;
    }
  }

  // Helper method to prepare rolling cages for export
  prepareRollingCagesForExport(rollingCages: RollingCage[]) {
    return rollingCages.map(cage => ({
      'ID': cage.id,
      'Ref No': cage.ref_no || 'N/A',
      'Election': cage.election,
      'To': cage.to,
      'Description': cage.description || 'N/A',
      'Type': cage.type || 'N/A',
      'Location': cage.location || 'N/A',
      'Quantity': cage.quantity || 0,
      'Packed': cage.packed ? 'Yes' : 'No',
      'Unpacked': cage.unpacked ? 'Yes' : 'No',
      'Packing Date': new Date(cage.packing_date).toLocaleDateString(),
      'Packing User': cage.packing_user || 'N/A',
      'Proofed By': cage.proofed_by || 'N/A',
      'Supply Package': cage.supply_package || 'N/A',
      'Created At': new Date(cage.created_at).toLocaleDateString(),
      'Updated At': new Date(cage.updated_at).toLocaleDateString()
    }));
  }
}

const rollingCageService = new RollingCageService();
export default rollingCageService; 