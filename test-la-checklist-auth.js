// Test L&A Checklist Authentication and Backend Connection
// Run this in browser console to test authentication and L&A checklist functionality

console.log('🧪 Testing L&A Checklist Authentication and Backend Connection...');

// Test credentials from the backend
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: '1234567890'
};

const API_BASE = 'http://localhost:8000/api';

async function testLogin() {
  console.log('🔐 Step 1: Testing login...');
  
  try {
    const response = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_CREDENTIALS)
    });
    
    const data = await response.json();
    console.log('📡 Login response:', data);
    
    if (response.ok && data.success && data.data?.token) {
      const token = data.data.token;
      localStorage.setItem('authToken', token);
      localStorage.setItem('userData', JSON.stringify(data.data.user));
      localStorage.setItem('isLoggedIn', 'true');
      localStorage.setItem('user', JSON.stringify({
        username: data.data.user.fullName,
        role: data.data.user.role,
        county: data.data.user.county,
        accessLevel: data.data.user.accessLevel
      }));
      
      console.log('✅ Login successful! Token stored.');
      console.log('👤 User:', data.data.user.fullName, '| Role:', data.data.user.role);
      return token;
    } else {
      console.error('❌ Login failed:', data.message);
      return null;
    }
  } catch (error) {
    console.error('❌ Login error:', error);
    return null;
  }
}

async function testLAChecklistEndpoints(token) {
  console.log('🔧 Step 2: Testing L&A Checklist endpoints...');
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };
  
  try {
    // Test 1: Get available assets
    console.log('📋 Testing: GET /api/la-checklist/available-assets');
    const assetsResponse = await fetch(`${API_BASE}/la-checklist/available-assets`, {
      headers
    });
    
    console.log('Status:', assetsResponse.status);
    if (assetsResponse.ok) {
      const assets = await assetsResponse.json();
      console.log('✅ Available assets:', assets.length, 'assets found');
      assets.slice(0, 3).forEach(asset => {
        console.log(`   - ${asset.asset_id}: ${asset.type} (${asset.status})`);
      });
      
      // Test 2: Get L&A sessions
      console.log('📋 Testing: GET /api/la-checklist/sessions');
      const sessionsResponse = await fetch(`${API_BASE}/la-checklist/sessions`, {
        headers
      });
      
      if (sessionsResponse.ok) {
        const sessions = await sessionsResponse.json();
        console.log('✅ L&A sessions:', sessions.length, 'sessions found');
        sessions.slice(0, 3).forEach(session => {
          console.log(`   - ${session.session_id}: Asset ${session.asset_id} (${session.status})`);
        });
      } else {
        console.log('⚠️ Sessions endpoint status:', sessionsResponse.status);
      }
      
      return true;
    } else {
      const errorData = await assetsResponse.json();
      console.error('❌ Assets endpoint failed:', assetsResponse.status, errorData);
      return false;
    }
  } catch (error) {
    console.error('❌ L&A Checklist endpoints test error:', error);
    return false;
  }
}

async function runFullTest() {
  console.log('🚀 Starting full L&A Checklist authentication test...');
  
  // Step 1: Login
  const token = await testLogin();
  if (!token) {
    console.error('❌ Test failed: Could not authenticate');
    return;
  }
  
  // Step 2: Test L&A endpoints
  const endpointsWorking = await testLAChecklistEndpoints(token);
  if (!endpointsWorking) {
    console.error('❌ Test failed: L&A Checklist endpoints not working');
    return;
  }
  
  console.log('🎉 SUCCESS! L&A Checklist authentication and backend connection working!');
  console.log('💡 Now try clicking "New Test Session" in the L&A Checklist pages.');
  console.log('🔑 Authentication token is stored in localStorage.');
}

// Run the test
runFullTest();
